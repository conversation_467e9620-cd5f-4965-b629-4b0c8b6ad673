#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星智能体服务
基于通用智能体框架
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class KaiyangStarService:
    """开阳星智能体服务"""
    
    def __init__(self):
        self.service_name = "开阳星智能体"
        self.version = "2.0.0"

        # 通用智能体框架
        self.universal_framework = None
        self.collaboration_system = None
        self.star_key = "kaiyang"
        self.autonomous_mode = False
        self.intelligence_level = "advanced"
        
        # 通用智能体框架
        self.universal_agent = None
        self._initialize_universal_agent()

        # 增强股票分析服务
        self.enhanced_stock_analysis_service = None
        self._initialize_enhanced_stock_analysis_service()

        # 初始化核心数据服务
        self._initialize_core_data_services()

        # 初始化并启动自动化数据收集
        self._initialize_automation_system()

        # 注册到消息总线
        self._register_to_message_bus()

        # 初始化通用智能体框架
        self._initialize_universal_agent_sync()

        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _initialize_universal_agent_sync(self):
        """同步初始化通用智能体 - 使用统一初始化器消除重复代码"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器的同步方法
            initialization_result = universal_agent_initializer.initialize_agent_sync(
                "开阳星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") == "success":
                logger.info(f"🧠 {self.service_name} 智能体框架同步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架同步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体同步初始化失败: {e}")
            self.intelligence_level = "basic"

    async def _initialize_universal_agent(self):
        """异步初始化通用智能体 - 保留异步接口"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器
            initialization_result = await universal_agent_initializer.initialize_complete_agent_framework(
                "开阳星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") in ["success", "partial_success"]:
                logger.info(f"🧠 {self.service_name} 智能体框架异步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架异步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体异步初始化失败: {e}")
            self.intelligence_level = "basic"

    def _initialize_enhanced_stock_analysis_service(self):
        """初始化增强股票分析服务"""
        try:
            from .services.enhanced_stock_analysis_service import KaiyangEnhancedStockAnalysisService
            self.enhanced_stock_analysis_service = KaiyangEnhancedStockAnalysisService()
            logger.info(f"📊 {self.service_name} 增强股票分析服务初始化完成")
        except Exception as e:
            logger.error(f"增强股票分析服务初始化失败: {e}")
            self.enhanced_stock_analysis_service = None

    def _initialize_core_data_services(self):
        """初始化核心数据服务"""
        try:
            # 综合数据收集服务
            from roles.kaiyang_star.services.comprehensive_data_collection_service import ComprehensiveDataCollectionService
            self.comprehensive_data_service = ComprehensiveDataCollectionService()

            # 股票数据增强服务
            from roles.kaiyang_star.services.stock_data_enhancement_service import StockDataEnhancementService
            self.enhancement_service = StockDataEnhancementService()

            logger.info("✅ 核心数据服务初始化完成")

        except Exception as e:
            logger.error(f"❌ 核心数据服务初始化失败: {e}")
            # 创建空的服务对象避免属性错误
            self.comprehensive_data_service = None
            self.enhancement_service = None

    def _initialize_automation_system(self):
        """初始化自动化系统 - 开阳星负责全市场5000+股票的数据收集"""
        try:
            logger.info("🚀 开阳星开始初始化全市场数据收集自动化系统...")

            # 开阳星负责所有数据收集：实时数据 + 历史数据 + 财务指标增强
            try:
                from roles.kaiyang_star.services.comprehensive_data_collection_service import ComprehensiveDataCollectionService
                from roles.kaiyang_star.services.stock_data_enhancement_service import StockDataEnhancementService

                # 初始化综合数据收集服务
                self.comprehensive_data_collection_service = ComprehensiveDataCollectionService()
                logger.info("📊 开阳星综合数据收集服务初始化完成")

                # 初始化数据增强服务
                self.stock_data_enhancement_service = StockDataEnhancementService()
                logger.info("💎 开阳星数据增强服务初始化完成")

                # 创建强大的定时任务系统
                import threading
                import schedule
                import time
                from datetime import datetime

                def run_optimized_collection():
                    """运行优化的数据收集调度 - 基于性能分析的最佳方案"""
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        async def optimized_collection_schedule():
                            current_time = datetime.now()
                            hour_minute = current_time.strftime("%H:%M")
                            weekday = current_time.weekday()  # 0=周一, 6=周日

                            # 🌅 06:00 - 全字段收集（实时数据）- 每日一次
                            if hour_minute == "06:00":
                                logger.info("🌅 开阳星06:00全字段收集（实时数据）...")
                                # 使用现有的全字段收集器
                                await self._run_full_field_collection()
                                logger.info("✅ 全字段收集完成")

                            # 📈 18:00 - 历史数据收集（当日K线）- 每日一次
                            elif hour_minute == "18:00":
                                logger.info("📈 开阳星18:00历史数据收集（当日K线）...")
                                result = await self.comprehensive_data_collection_service.collect_historical_data_batch(limit=0)  # 0表示全部
                                if result.get("success"):
                                    logger.info(f"✅ 历史数据收集: {result.get('successful_count', 0)}只股票")

                            # 🔧 19:00 - TA-Lib增量计算 - 每日一次
                            elif hour_minute == "19:00":
                                logger.info("🔧 开阳星19:00 TA-Lib增量计算...")
                                await self._run_incremental_talib_calculation()
                                logger.info("✅ TA-Lib增量计算完成")

                            # 🔍 20:00 - 数据质量检查 - 每日一次
                            elif hour_minute == "20:00":
                                logger.info("🔍 开阳星20:00数据质量检查...")
                                await self._run_data_quality_check()
                                logger.info("✅ 数据质量检查完成")

                            # 🏗️ 02:00 - 历史数据补齐（周末） - 每周一次
                            elif hour_minute == "02:00" and weekday == 6:  # 周日凌晨
                                logger.info("🏗️ 开阳星周末历史数据补齐...")
                                await self._run_historical_data_backfill()
                                logger.info("✅ 历史数据补齐完成")

                        loop.run_until_complete(optimized_collection_schedule())
                        loop.close()
                    except Exception as e:
                        logger.error(f"开阳星优化收集调度失败: {e}")

                # 设置定时任务 - 每分钟检查一次
                schedule.every().minute.do(run_optimized_collection)

                # 启动调度器线程
                def run_scheduler():
                    logger.info("📅 开阳星数据收集调度器启动")
                    while True:
                        schedule.run_pending()
                        time.sleep(60)  # 每分钟检查一次

                scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
                scheduler_thread.start()

                logger.info("✅ 开阳星优化自动化数据收集系统启动成功")
                logger.info("📊 优化方案: 06:00全字段 + 18:00历史 + 19:00计算 + 20:00检查")
                logger.info("🎯 负责范围: 5522只股票完整数据 + 历史数据补齐")

            except Exception as e:
                logger.error(f"开阳星自动化启动失败: {e}")

        except Exception as e:
            logger.error(f"自动化系统初始化失败: {e}")

    async def _run_full_field_collection(self):
        """运行全字段收集"""
        try:
            # 启动全字段收集器
            import subprocess
            import sys

            logger.info("🚀 启动全字段收集器...")

            # 使用现有的全字段收集器
            process = subprocess.Popen([
                sys.executable, 'full_field_collector.py'
            ], cwd='backend')

            # 等待完成（最多30分钟）
            try:
                process.wait(timeout=1800)  # 30分钟超时
                logger.info("✅ 全字段收集器完成")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.warning("⚠️ 全字段收集器超时，已终止")

        except Exception as e:
            logger.error(f"❌ 全字段收集失败: {e}")

    async def _run_incremental_talib_calculation(self):
        """运行TA-Lib增量计算"""
        try:
            # 启动智能增量TA-Lib计算器
            import subprocess
            import sys

            logger.info("🔧 启动TA-Lib增量计算器...")

            process = subprocess.Popen([
                sys.executable, 'smart_incremental_talib_calculator.py'
            ], cwd='backend')

            # 等待完成（最多15分钟）
            try:
                process.wait(timeout=900)  # 15分钟超时
                logger.info("✅ TA-Lib增量计算完成")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.warning("⚠️ TA-Lib计算超时，已终止")

        except Exception as e:
            logger.error(f"❌ TA-Lib增量计算失败: {e}")

    async def _run_data_quality_check(self):
        """运行数据质量检查"""
        try:
            logger.info("🔍 开始数据质量检查...")

            # 检查数据完整性
            await self.comprehensive_data_collection_service._check_data_integrity()

            logger.info("✅ 数据质量检查完成")

        except Exception as e:
            logger.error(f"❌ 数据质量检查失败: {e}")

    async def _run_historical_data_backfill(self):
        """运行历史数据补齐"""
        try:
            logger.info("🏗️ 开始历史数据补齐...")

            # 获取缺失历史数据的股票
            missing_stocks = await self._get_missing_historical_stocks()

            if missing_stocks:
                logger.info(f"📊 发现{len(missing_stocks)}只股票缺失历史数据")

                # 分批补齐，每次200只股票
                batch_size = 200
                for i in range(0, len(missing_stocks), batch_size):
                    batch = missing_stocks[i:i+batch_size]
                    logger.info(f"🔄 补齐第{i//batch_size + 1}批: {len(batch)}只股票")

                    result = await self.comprehensive_data_collection_service._collect_historical_data_batch(batch)

                    # 避免请求过快
                    await asyncio.sleep(5)

                logger.info("✅ 历史数据补齐完成")
            else:
                logger.info("✅ 所有股票历史数据完整，无需补齐")

        except Exception as e:
            logger.error(f"❌ 历史数据补齐失败: {e}")

    async def _get_missing_historical_stocks(self):
        """获取缺失历史数据的股票列表"""
        try:
            import sqlite3

            # 从基础信息数据库获取所有股票
            master_conn = sqlite3.connect('backend/data/stock_master.db')
            cursor = master_conn.cursor()
            cursor.execute("SELECT DISTINCT symbol FROM stock_basic_info WHERE symbol IS NOT NULL")
            all_stocks = set([row[0] for row in cursor.fetchall()])
            master_conn.close()

            # 从历史数据库获取已有股票
            hist_conn = sqlite3.connect('backend/data/stock_historical.db')
            cursor = hist_conn.cursor()
            cursor.execute("SELECT DISTINCT stock_code FROM daily_data")
            existing_stocks = set([row[0] for row in cursor.fetchall()])
            hist_conn.close()

            # 计算缺失的股票
            missing_stocks = list(all_stocks - existing_stocks)

            logger.info(f"📊 总股票数: {len(all_stocks)}, 已有历史数据: {len(existing_stocks)}, 缺失: {len(missing_stocks)}")

            return missing_stocks

        except Exception as e:
            logger.error(f"❌ 获取缺失股票列表失败: {e}")
            return []

    async def start(self):
        """启动开阳星服务"""
        try:
            logger.info("🚀 启动开阳星服务...")

            # 启动自动化系统
            await self._initialize_automation()

            logger.info("✅ 开阳星服务启动成功")

            # 保持服务运行
            while True:
                await asyncio.sleep(60)  # 每分钟检查一次

        except KeyboardInterrupt:
            logger.info("🛑 开阳星服务已停止")
        except Exception as e:
            logger.error(f"❌ 开阳星服务启动失败: {e}")

    async def start_data_collection(self):
        """启动数据收集服务"""
        try:
            logger.info("📊 启动开阳星数据收集服务...")

            # 立即执行一次全面数据收集
            await self._run_immediate_collection()

            logger.info("✅ 数据收集服务启动完成")

        except Exception as e:
            logger.error(f"❌ 数据收集服务启动失败: {e}")

    async def _run_immediate_collection(self):
        """立即执行全面数据收集"""
        try:
            logger.info("🔄 开始立即数据收集...")

            # 1. 全字段收集
            logger.info("📊 步骤1: 全字段收集")
            await self._run_full_field_collection()

            # 2. 历史数据补齐
            logger.info("📈 步骤2: 历史数据补齐")
            await self._run_historical_data_backfill()

            # 3. TA-Lib计算
            logger.info("🔧 步骤3: TA-Lib计算")
            await self._run_incremental_talib_calculation()

            # 4. 数据质量检查
            logger.info("🔍 步骤4: 数据质量检查")
            await self._run_data_quality_check()

            logger.info("✅ 立即数据收集完成")

        except Exception as e:
            logger.error(f"❌ 立即数据收集失败: {e}")

    def _register_to_message_bus(self):
        """注册到消息总线"""
        try:
            from core.agent_message_bus import agent_message_bus

            # 注册开阳星到消息总线
            agent_message_bus.register_agent(
                agent_name="开阳星",
                agent_instance=self,
                subscriptions=["stock_selection", "data_push", "collaboration", "request"]
            )

            logger.info("📡 开阳星已注册到消息总线")

        except Exception as e:
            logger.error(f"注册到消息总线失败: {e}")
    
    async def start_autonomous_mode(self):
        """启动自主模式"""
        if self.autonomous_mode:
            return

        self.autonomous_mode = True
        logger.info(f"🚀 启动 {self.service_name} 自主模式")

        if self.universal_agent:
            asyncio.create_task(self.universal_agent.start_agent())
    
    async def stop_autonomous_mode(self):
        """停止自主模式"""
        self.autonomous_mode = False
        
        if self.universal_agent:
            await self.universal_agent.stop_agent()
        
        logger.info(f"⏹️ {self.service_name} 自主模式已停止")
    
    async def intelligent_analysis(self, input_data: Dict[str, Any], 
                                 analysis_type: str = "general_analysis") -> Dict[str, Any]:
        """智能分析"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🧠 {self.service_name} 开始智能分析: {analysis_type}")
            
            analysis_result = await self.universal_agent.intelligent_analysis(
                input_data, analysis_type
            )
            
            # 添加专业处理
            specialized_result = analysis_result.copy()
            specialized_result["specialized_insights"] = {
                "star_perspective": "开阳星专业视角",
                "professional_focus": "kaiyang_analysis"
            }
            
            return {
                "success": True,
                "service": self.service_name,
                "analysis_result": specialized_result,
                "framework_version": "universal_v2.0"
            }
            
        except Exception as e:
            logger.error(f"{self.service_name} 智能分析失败: {e}")
            return {"error": str(e)}
    
    async def collaborative_request(self, target_agents: List[str], 
                                  request_data: Dict[str, Any]) -> Dict[str, Any]:
        """协作请求"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🤝 {self.service_name} 发起协作: {target_agents}")
            
            collaboration_result = await self.universal_agent.collaborative_request(
                target_agents, "collaboration_request", request_data
            )
            
            return {
                "success": True,
                "collaboration_result": collaboration_result
            }
            
        except Exception as e:
            logger.error(f"协作请求失败: {e}")
            return {"error": str(e)}
    
    async def adaptive_learning(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """自适应学习"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"📚 {self.service_name} 自适应学习")
            
            learning_success = await self.universal_agent.adaptive_learning_from_feedback(feedback)
            
            return {
                "success": learning_success,
                "learning_completed": True,
                "service": self.service_name
            }
            
        except Exception as e:
            logger.error(f"自适应学习失败: {e}")
            return {"error": str(e)}
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        try:
            status = {
                "service_name": self.service_name,
                "version": self.version,
                "star_key": self.star_key,
                "autonomous_mode": self.autonomous_mode,
                "intelligence_level": self.intelligence_level,
                "timestamp": datetime.now().isoformat()
            }
            
            if self.universal_agent:
                universal_status = await self.universal_agent.get_agent_status()
                status["universal_framework"] = universal_status
            
            return status
            
        except Exception as e:
            logger.error(f"获取智能体状态失败: {e}")
            return {"error": str(e)}

    def receive_message(self, message):
        """接收来自其他智能体的消息"""
        try:
            from core.independent_agent_system import AgentMessage

            if not isinstance(message, AgentMessage):
                logger.warning(f"收到非标准消息: {type(message)}")
                return

            logger.info(f"📥 开阳星收到消息: {message.from_agent} → {message.message_type}")

            # 处理不同类型的消息
            if message.message_type == "data_push" and message.from_agent == "天枢星":
                asyncio.create_task(self._handle_tianshu_data_push(message))
            elif message.message_type == "request":
                asyncio.create_task(self._handle_request_message(message))
            elif message.message_type == "collaboration":
                asyncio.create_task(self._handle_collaboration_message(message))
            else:
                logger.info(f"未处理的消息类型: {message.message_type}")

        except Exception as e:
            logger.error(f"处理消息失败: {e}")

    async def _handle_tianshu_data_push(self, message):
        """处理天枢星数据推送"""
        try:
            content = message.content
            news_data = content.get("news_data", {})
            market_data = content.get("market_data", {})

            logger.info(f"📰 收到天枢星数据推送: 新闻{len(news_data.get('news_items', []))}条")

            # 基于新闻数据触发选股分析
            if news_data.get("urgent_alerts", 0) > 0:
                await self._trigger_urgent_stock_analysis(news_data)

            # 更新市场环境感知
            if market_data:
                await self._update_market_environment(market_data)

        except Exception as e:
            logger.error(f"处理天枢星数据推送失败: {e}")

    async def _handle_request_message(self, message):
        """处理请求消息"""
        try:
            content = message.content
            request_type = content.get("type", "unknown")

            if request_type == "stock_selection_request":
                # 执行股票筛选
                criteria = content.get("criteria", {})
                result = await self._perform_stock_screening(criteria)

                # 发送响应
                await self._send_response(message, result)

        except Exception as e:
            logger.error(f"处理请求消息失败: {e}")

    async def _trigger_urgent_stock_analysis(self, news_data):
        """基于紧急新闻触发股票分析"""
        try:
            hot_sectors = news_data.get("hot_sectors", [])

            # 针对热门板块进行选股
            criteria = {
                "sectors": hot_sectors,
                "urgency": "high",
                "max_results": 20
            }

            result = await self._perform_stock_screening(criteria)

            if result.get("qualified_stocks"):
                # 触发七星协作工作流
                from core.agent_message_bus import agent_message_bus
                workflow_id = await agent_message_bus.trigger_seven_stars_workflow(result)

                logger.info(f"🚀 基于紧急新闻触发七星工作流: {workflow_id}")

        except Exception as e:
            logger.error(f"紧急股票分析失败: {e}")

    async def screen_stocks(self, criteria: Dict[str, Any] = None) -> Dict[str, Any]:
        """股票筛选 - 公共接口"""
        try:
            logger.info(f"🔍 开阳星开始股票筛选，条件: {criteria}")

            # 使用默认筛选条件
            if not criteria:
                criteria = {
                    "min_market_cap": 1000000000,  # 10亿市值
                    "max_pe_ratio": 50,
                    "min_volume": 1000000,
                    "limit": 50
                }

            result = await self._perform_stock_screening(criteria)

            if result.get("qualified_stocks"):
                logger.info(f"✅ 开阳星筛选完成: {len(result['qualified_stocks'])}只股票")

            return {
                "success": True,
                "stocks": result.get("qualified_stocks", []),
                "criteria": criteria,
                "total_found": len(result.get("qualified_stocks", [])),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"股票筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stocks": [],
                "timestamp": datetime.now().isoformat()
            }

    async def collect_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """收集股票数据 - 公共接口"""
        try:
            logger.info(f"📊 开阳星收集股票数据: {stock_code}")

            # 调用实时数据收集
            result = await self.collect_real_time_data([stock_code], limit=1)

            if result.get("success"):
                return {
                    "success": True,
                    "stock_code": stock_code,
                    "data": result.get("data", {}),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "数据收集失败"),
                    "stock_code": stock_code,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"收集股票数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code,
                "timestamp": datetime.now().isoformat()
            }

    async def _perform_stock_screening(self, criteria):
        """执行股票筛选"""
        try:
            from roles.kaiyang_star.services.real_stock_screening_service import RealStockScreeningService

            screening_service = RealStockScreeningService()
            result = await screening_service.intelligent_screening(criteria, {})

            return result

        except Exception as e:
            logger.error(f"股票筛选失败: {e}")
            return {"qualified_stocks": [], "error": str(e)}

    async def collect_real_time_data(self, stock_codes: List[str] = None, limit: int = 100) -> Dict[str, Any]:
        """收集实时数据 - 调用通用智能体框架中的方法"""
        try:
            logger.info(f"🔄 开阳星开始收集实时数据，股票数量: {len(stock_codes) if stock_codes else limit}")

            # 使用通用智能体框架中的独立智能体
            if self.universal_agent and hasattr(self.universal_agent, 'independent_agent'):
                result = await self.universal_agent.independent_agent.collect_real_time_data(
                    stock_codes=stock_codes,
                    limit=limit
                )

                if result.get("success"):
                    logger.info(f"✅ 开阳星实时数据收集成功: {result.get('successful_count', 0)}/{result.get('total_stocks', 0)}")
                    return result
                else:
                    logger.warning(f"⚠️ 开阳星实时数据收集部分失败: {result.get('error', '未知错误')}")
                    return result
            else:
                # 降级模式：直接调用独立智能体
                from roles.kaiyang_star.independent_kaiyang_agent import kaiyang_independent_agent

                result = await kaiyang_independent_agent.collect_real_time_data(
                    stock_codes=stock_codes,
                    limit=limit
                )

                logger.info(f"✅ 开阳星实时数据收集完成（降级模式）")
                return result

        except Exception as e:
            logger.error(f"开阳星实时数据收集失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_stocks": 0,
                "successful_count": 0,
                "failed_count": len(stock_codes) if stock_codes else limit
            }

    async def _enhance_financial_data(self):
        """增强财务数据 - 收集PE、PB、ROE等财务指标"""
        try:
            logger.info("💎 开始财务数据增强...")

            # 获取所有股票代码
            import sqlite3
            conn = sqlite3.connect('backend/data/stock_master.db')
            cursor = conn.cursor()

            # 获取需要增强的股票（优先处理财务指标缺失的股票）
            cursor.execute("""
                SELECT stock_code FROM stock_basic_info
                WHERE roe IS NULL OR roe = 0 OR debt_ratio IS NULL OR debt_ratio = 0
                ORDER BY total_market_cap DESC
                LIMIT 500
            """)

            stock_codes = [row[0] for row in cursor.fetchall()]
            conn.close()

            if not stock_codes:
                logger.info("📊 所有股票财务数据已完整")
                return

            logger.info(f"📊 需要增强财务数据的股票: {len(stock_codes)}只")

            # 批量增强财务数据
            batch_size = 50
            enhanced_count = 0

            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]

                try:
                    # 使用数据增强服务
                    result = await self.stock_data_enhancement_service.enhance_batch_financial_data(batch_codes)

                    if result.get("success"):
                        batch_enhanced = result.get("enhanced_count", 0)
                        enhanced_count += batch_enhanced
                        logger.info(f"✅ 批次 {i//batch_size + 1}: 增强 {batch_enhanced}/{len(batch_codes)} 只股票")
                    else:
                        logger.warning(f"⚠️ 批次 {i//batch_size + 1} 增强失败: {result.get('error', '未知错误')}")

                except Exception as e:
                    logger.error(f"❌ 批次 {i//batch_size + 1} 处理异常: {e}")

                # 避免API限制
                import asyncio
                await asyncio.sleep(1)

            logger.info(f"💎 财务数据增强完成: 总计增强 {enhanced_count} 只股票")

        except Exception as e:
            logger.error(f"❌ 财务数据增强失败: {e}")

    async def enhance_stock_financial_data(self, stock_codes: list = None, limit: int = 100):
        """公共API：增强股票财务数据"""
        try:
            if not hasattr(self, 'stock_data_enhancement_service'):
                return {"success": False, "error": "数据增强服务未初始化"}

            if not stock_codes:
                # 获取需要增强的股票
                import sqlite3
                conn = sqlite3.connect('backend/data/stock_master.db')
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT stock_code FROM stock_basic_info
                    WHERE roe IS NULL OR roe = 0 OR debt_ratio IS NULL OR debt_ratio = 0
                    ORDER BY total_market_cap DESC
                    LIMIT ?
                """, (limit,))

                stock_codes = [row[0] for row in cursor.fetchall()]
                conn.close()

            if not stock_codes:
                return {"success": True, "message": "所有股票财务数据已完整", "enhanced_count": 0}

            # 批量增强
            result = await self.stock_data_enhancement_service.enhance_batch_financial_data(stock_codes)
            return result

        except Exception as e:
            logger.error(f"增强股票财务数据失败: {e}")
            return {"success": False, "error": str(e)}

    # ==================== 开阳星强化功能 ====================

    async def complete_missing_historical_data(self):
        """补齐缺失的历史数据 - 开阳星核心功能"""
        try:
            logger.info("🚀 开阳星开始补齐缺失的历史数据")

            result = await self.comprehensive_data_service.complete_missing_historical_data(
                batch_size=10,
                max_concurrent=2
            )

            return result

        except Exception as e:
            logger.error(f"❌ 历史数据补齐失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def complete_talib_calculation(self):
        """完成TA-Lib技术指标计算 - 开阳星核心功能"""
        try:
            logger.info("🚀 开阳星开始完整TA-Lib技术指标计算")

            result = await self.enhancement_service.complete_talib_calculation_for_all_stocks()

            return result

        except Exception as e:
            logger.error(f"❌ TA-Lib计算失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def enhance_financial_data_comprehensive(self):
        """增强财务数据 - 开阳星核心功能"""
        try:
            logger.info("🚀 开阳星开始增强财务数据")

            result = await self.enhancement_service.enhance_financial_data_for_all_stocks(
                batch_size=100,
                max_concurrent=8
            )

            return result

        except Exception as e:
            logger.error(f"❌ 财务数据增强失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def run_comprehensive_enhancement(self):
        """运行综合强化程序 - 开阳星全面提升"""
        try:
            logger.info("🌟 开阳星开始综合强化程序")
            logger.info("="*80)

            enhancement_results = {}

            # 1. 补齐历史数据
            logger.info("📈 第1步: 补齐历史数据")
            historical_result = await self.complete_missing_historical_data()
            enhancement_results["historical_data"] = historical_result

            if historical_result.get("success"):
                logger.info("✅ 历史数据补齐完成")
            else:
                logger.warning("⚠️ 历史数据补齐部分失败")

            # 2. 完成TA-Lib计算
            logger.info("📊 第2步: 完成TA-Lib技术指标计算")
            talib_result = await self.complete_talib_calculation()
            enhancement_results["talib_calculation"] = talib_result

            if talib_result.get("success"):
                logger.info("✅ TA-Lib计算完成")
            else:
                logger.warning("⚠️ TA-Lib计算部分失败")

            # 3. 增强财务数据
            logger.info("💰 第3步: 增强财务数据")
            financial_result = await self.enhance_financial_data_comprehensive()
            enhancement_results["financial_data"] = financial_result

            if financial_result.get("success"):
                logger.info("✅ 财务数据增强完成")
            else:
                logger.warning("⚠️ 财务数据增强部分失败")

            # 统计总体结果
            total_success = all(result.get("success", False) for result in enhancement_results.values())

            logger.info("\n" + "="*80)
            logger.info("🎉 开阳星综合强化程序完成!")
            logger.info(f"📊 总体状态: {'✅ 全部成功' if total_success else '⚠️ 部分成功'}")

            for step_name, result in enhancement_results.items():
                status = "✅ 成功" if result.get("success") else "❌ 失败"
                logger.info(f"  {step_name}: {status}")

            return {
                "success": total_success,
                "message": "综合强化程序完成",
                "results": enhancement_results
            }

        except Exception as e:
            logger.error(f"❌ 综合强化程序失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    # ==================== 瑶光星学习协调专用方法 ====================

    async def select_stocks_for_learning(self, learning_config: Dict[str, Any]) -> Dict[str, Any]:
        """为学习协调选择股票 - 瑶光星专用接口"""
        try:
            logger.info(f"📊 开阳星为学习协调选择股票: {learning_config}")

            target_count = learning_config.get("target_count", 10)
            selection_mode = learning_config.get("selection_mode", "comprehensive")
            depth = learning_config.get("depth", "deep")

            # 基于学习模式调整选股策略
            if selection_mode == "daily_learning":
                # 日常学习：选择活跃度高的股票
                selection_criteria = {
                    "volume_threshold": 1000000,  # 成交量阈值
                    "price_change_range": [0.02, 0.08],  # 价格变动范围
                    "market_cap_min": 1000000000,  # 最小市值
                    "technical_indicators": ["RSI", "MACD", "BOLL"]
                }
            elif selection_mode == "pattern_recognition":
                # 模式识别：选择技术形态明显的股票
                selection_criteria = {
                    "pattern_types": ["突破", "回调", "震荡"],
                    "volatility_range": [0.015, 0.05],
                    "technical_indicators": ["MA", "RSI", "KDJ", "MACD"]
                }
            elif selection_mode == "risk_assessment":
                # 风险评估：选择不同风险等级的股票
                selection_criteria = {
                    "risk_levels": ["low", "medium", "high"],
                    "beta_range": [0.5, 2.0],
                    "volatility_analysis": True
                }
            else:
                # 综合模式
                selection_criteria = {
                    "comprehensive_analysis": True,
                    "multi_factor_screening": True,
                    "technical_indicators": ["MA", "RSI", "MACD", "BOLL", "KDJ"]
                }

            # 执行智能选股
            screening_result = await self._perform_learning_stock_screening(
                selection_criteria, target_count, depth
            )

            # 增强选股结果
            if screening_result.get("success") and self.enhancement_service:
                enhanced_result = await self.enhancement_service.enhance_selected_stocks_for_learning(
                    screening_result.get("selected_stocks", [])
                )
                screening_result["enhancement_data"] = enhanced_result

            # 记录学习选股历史
            learning_record = {
                "session_type": "learning_coordination",
                "selection_mode": selection_mode,
                "target_count": target_count,
                "actual_count": len(screening_result.get("selected_stocks", [])),
                "criteria": selection_criteria,
                "timestamp": datetime.now().isoformat()
            }

            # 自适应学习：从选股结果中学习
            if self.universal_agent:
                await self.universal_agent.adaptive_learning_from_feedback({
                    "task_type": "stock_selection_for_learning",
                    "result": screening_result,
                    "config": learning_config,
                    "performance_metrics": screening_result.get("performance_metrics", {})
                })

            return {
                "success": screening_result.get("success", False),
                "selected_stocks": screening_result.get("selected_stocks", []),
                "selection_criteria": selection_criteria,
                "learning_record": learning_record,
                "enhancement_data": screening_result.get("enhancement_data", {}),
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"开阳星学习协调选股失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": self.service_name
            }

    async def select_stocks_for_backtest(self, backtest_config: Dict[str, Any]) -> Dict[str, Any]:
        """为回测协调选择股票 - 瑶光星专用接口"""
        try:
            logger.info(f"🔄 开阳星为回测协调选择股票: {backtest_config}")

            date_range = backtest_config.get("date_range", [])
            strategy = backtest_config.get("strategy", "")
            risk_level = backtest_config.get("risk_level", "medium")

            # 基于回测策略调整选股
            if strategy == "momentum_strategy":
                selection_criteria = {
                    "momentum_indicators": ["RSI", "MACD"],
                    "trend_strength": "strong",
                    "volume_confirmation": True
                }
            elif strategy == "mean_reversion":
                selection_criteria = {
                    "oversold_conditions": True,
                    "support_resistance": True,
                    "volatility_analysis": True
                }
            elif strategy == "trend_following":
                selection_criteria = {
                    "trend_indicators": ["MA", "MACD", "ADX"],
                    "trend_duration": "medium_to_long",
                    "breakout_patterns": True
                }
            else:
                selection_criteria = {
                    "balanced_selection": True,
                    "multi_strategy_compatible": True
                }

            # 执行历史回测选股
            backtest_screening = await self._perform_backtest_stock_screening(
                selection_criteria, date_range, strategy, risk_level
            )

            return {
                "success": backtest_screening.get("success", False),
                "selected_stocks": backtest_screening.get("selected_stocks", []),
                "selection_criteria": selection_criteria,
                "backtest_period": date_range,
                "strategy_alignment": strategy,
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"开阳星回测协调选股失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": self.service_name
            }

    async def _perform_learning_stock_screening(self, criteria: Dict[str, Any],
                                              target_count: int, depth: str) -> Dict[str, Any]:
        """执行学习导向的股票筛选"""
        try:
            # 使用真实的股票筛选服务
            if hasattr(self, 'comprehensive_data_service') and self.comprehensive_data_service:
                screening_result = await self.comprehensive_data_service.intelligent_stock_screening({
                    "criteria": criteria,
                    "target_count": target_count,
                    "analysis_depth": depth,
                    "learning_oriented": True
                })

                return screening_result
            else:
                # 降级处理：基础筛选
                return await self._basic_learning_screening(criteria, target_count)

        except Exception as e:
            logger.error(f"学习股票筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "selected_stocks": []
            }

    async def _perform_backtest_stock_screening(self, criteria: Dict[str, Any],
                                              date_range: List[str], strategy: str,
                                              risk_level: str) -> Dict[str, Any]:
        """执行回测导向的股票筛选"""
        try:
            # 使用历史数据进行回测筛选
            if hasattr(self, 'enhancement_service') and self.enhancement_service:
                screening_result = await self.enhancement_service.historical_stock_screening({
                    "criteria": criteria,
                    "date_range": date_range,
                    "strategy": strategy,
                    "risk_level": risk_level,
                    "backtest_oriented": True
                })

                return screening_result
            else:
                # 降级处理：基础历史筛选
                return await self._basic_backtest_screening(criteria, date_range)

        except Exception as e:
            logger.error(f"回测股票筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "selected_stocks": []
            }

    async def _basic_learning_screening(self, criteria: Dict[str, Any], target_count: int) -> Dict[str, Any]:
        """基础学习筛选（降级处理）"""
        try:
            # 从数据库获取活跃股票
            from backend.data.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # 获取基础股票列表
            stocks_query = """
            SELECT DISTINCT stock_code, stock_name
            FROM stock_realtime
            WHERE volume > 1000000
            AND price > 1.0
            ORDER BY volume DESC
            LIMIT ?
            """

            stocks_data = db_manager.execute_query("stock_realtime.db", stocks_query, (target_count,))

            selected_stocks = [
                {
                    "stock_code": row[0],
                    "stock_name": row[1],
                    "selection_reason": "基础筛选 - 成交量活跃"
                }
                for row in stocks_data
            ]

            return {
                "success": True,
                "selected_stocks": selected_stocks,
                "screening_method": "basic_learning_screening"
            }

        except Exception as e:
            logger.error(f"基础学习筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "selected_stocks": []
            }

    async def _basic_backtest_screening(self, criteria: Dict[str, Any], date_range: List[str]) -> Dict[str, Any]:
        """基础回测筛选（降级处理）"""
        try:
            # 从历史数据库获取股票
            from backend.data.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # 获取历史期间活跃的股票
            stocks_query = """
            SELECT DISTINCT stock_code, stock_name
            FROM stock_historical
            WHERE date BETWEEN ? AND ?
            AND volume > 500000
            ORDER BY volume DESC
            LIMIT 20
            """

            stocks_data = db_manager.execute_query(
                "stock_historical.db",
                stocks_query,
                (date_range[0], date_range[1])
            )

            selected_stocks = [
                {
                    "stock_code": row[0],
                    "stock_name": row[1],
                    "selection_reason": f"历史期间活跃 ({date_range[0]} - {date_range[1]})"
                }
                for row in stocks_data
            ]

            return {
                "success": True,
                "selected_stocks": selected_stocks,
                "screening_method": "basic_backtest_screening"
            }

        except Exception as e:
            logger.error(f"基础回测筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "selected_stocks": []
            }


# 全局实例
kaiyang_star_service = KaiyangStarService()
