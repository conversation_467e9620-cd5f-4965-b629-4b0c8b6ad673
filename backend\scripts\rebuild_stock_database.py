#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据库重建脚本
重新收集所有股票数据，确保130+字段完整收集
"""

import asyncio
import sqlite3
import os
import sys
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import pandas as pd
import aiohttp
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.database.unified_database_path_manager import UnifiedDatabasePathManager
from services.data.eastmoney_api import EastmoneyAPI
from scripts.technical_indicators_calculator import TechnicalIndicatorsCalculator
from scripts.financial_indicators_calculator import FinancialIndicatorsCalculator
from scripts.enhanced_eastmoney_collector import EnhancedEastmoneyCollector

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockDatabaseRebuilder:
    """股票数据库重建器"""
    
    def __init__(self):
        self.path_manager = UnifiedDatabasePathManager()
        self.eastmoney_api = EastmoneyAPI()

        # 初始化计算器和收集器
        self.technical_calculator = TechnicalIndicatorsCalculator()
        self.financial_calculator = FinancialIndicatorsCalculator()
        self.enhanced_collector = None  # 将在异步环境中初始化

        # 数据库路径
        self.stock_db_path = self.path_manager.get_database_path("stock_master")
        self.realtime_db_path = self.path_manager.get_database_path("realtime_stock_data")
        self.daily_db_path = self.path_manager.get_database_path("daily_stock_data")

        logger.info(f"数据库路径: {self.stock_db_path}")
        
    async def rebuild_all_databases(self):
        """重建所有数据库"""
        logger.info("🚀 开始重建股票数据库...")

        try:
            # 初始化增强收集器
            async with EnhancedEastmoneyCollector() as collector:
                self.enhanced_collector = collector

                # 1. 创建数据库表结构
                await self.create_database_schema()

                # 2. 获取股票列表
                stock_list = await self.get_stock_list()
                logger.info(f"📊 获取到 {len(stock_list)} 只股票")

                # 3. 研究未知字段（仅在开发阶段）
                if len(stock_list) > 0:
                    research_result = await collector.research_unknown_fields(stock_list[:5])
                    if research_result:
                        logger.info(f"🔍 字段研究结果: {research_result.get('total_fields_returned', 0)}个字段")

                # 4. 收集股票基础信息
                await self.collect_stock_basic_info(stock_list)

                # 5. 收集历史数据
                await self.collect_historical_data(stock_list)

                # 6. 收集实时数据
                await self.collect_realtime_data(stock_list)

            logger.info("✅ 数据库重建完成！")

        except Exception as e:
            logger.error(f"❌ 数据库重建失败: {e}")
            raise
    
    async def create_database_schema(self):
        """创建数据库表结构"""
        logger.info("📋 创建数据库表结构...")
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.stock_db_path), exist_ok=True)
        
        # 删除旧表，重新创建
        conn = sqlite3.connect(self.stock_db_path)
        conn.execute("DROP TABLE IF EXISTS stock_info")
        conn.commit()
        conn.close()

        # 同样处理其他数据库
        conn = sqlite3.connect(self.daily_db_path)
        conn.execute("DROP TABLE IF EXISTS daily_data")
        conn.commit()
        conn.close()

        conn = sqlite3.connect(self.realtime_db_path)
        conn.execute("DROP TABLE IF EXISTS realtime_quotes")
        conn.commit()
        conn.close()

        # 股票基础信息表 - 完整130+字段
        stock_info_schema = """
        CREATE TABLE stock_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stock_code TEXT UNIQUE NOT NULL,
            stock_name TEXT,
            market TEXT,
            exchange TEXT,
            industry TEXT,
            sector TEXT,
            concept TEXT,
            area TEXT,

            -- 基础价格信息 (20个字段)
            current_price REAL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            close_price REAL,
            pre_close REAL,
            highest_today REAL,
            lowest_today REAL,
            highest_52week REAL,
            lowest_52week REAL,
            highest_year REAL,
            lowest_year REAL,
            avg_price REAL,
            weighted_avg_price REAL,
            settlement_price REAL,
            limit_up REAL,
            limit_down REAL,
            suspension_status INTEGER,
            trading_status TEXT,
            listing_date DATE,

            -- 涨跌信息 (15个字段)
            change_amount REAL,
            change_percent REAL,
            change_5min REAL,
            change_15min REAL,
            change_30min REAL,
            change_60min REAL,
            change_today REAL,
            change_3day REAL,
            change_5day REAL,
            change_10day REAL,
            change_20day REAL,
            change_60day REAL,
            change_year REAL,
            change_ytd REAL,
            volatility REAL,

            -- 成交信息 (20个字段)
            volume REAL,
            turnover REAL,
            turnover_rate REAL,
            volume_ratio REAL,
            avg_volume_5day REAL,
            avg_volume_10day REAL,
            avg_volume_20day REAL,
            avg_turnover_5day REAL,
            avg_turnover_10day REAL,
            avg_turnover_20day REAL,
            bid_volume REAL,
            ask_volume REAL,
            total_bid_volume REAL,
            total_ask_volume REAL,
            inner_volume REAL,
            outer_volume REAL,
            neutral_volume REAL,
            large_order_volume REAL,
            medium_order_volume REAL,
            small_order_volume REAL,

            -- 市值信息 (15个字段)
            market_cap REAL,
            circulating_market_cap REAL,
            total_shares REAL,
            circulating_shares REAL,
            free_float_shares REAL,
            restricted_shares REAL,
            a_shares REAL,
            b_shares REAL,
            h_shares REAL,
            share_premium REAL,
            book_value_per_share REAL,
            tangible_book_value_per_share REAL,
            cash_per_share REAL,
            revenue_per_share REAL,
            earnings_per_share REAL,

            -- 财务指标 (25个字段)
            pe_ratio REAL,
            pe_ttm REAL,
            pe_static REAL,
            pe_dynamic REAL,
            pb_ratio REAL,
            ps_ratio REAL,
            pcf_ratio REAL,
            peg_ratio REAL,
            roe REAL,
            roe_ttm REAL,
            roa REAL,
            roic REAL,
            gross_profit_margin REAL,
            operating_profit_margin REAL,
            net_profit_margin REAL,
            ebitda_margin REAL,
            operating_margin REAL,
            pretax_margin REAL,
            tax_rate REAL,
            interest_coverage REAL,
            debt_to_equity REAL,
            debt_to_assets REAL,
            equity_multiplier REAL,
            asset_turnover REAL,
            inventory_turnover REAL,

            -- 技术指标 (30个字段)
            ma5 REAL,
            ma10 REAL,
            ma20 REAL,
            ma30 REAL,
            ma60 REAL,
            ma120 REAL,
            ma250 REAL,
            ema5 REAL,
            ema10 REAL,
            ema12 REAL,
            ema20 REAL,
            ema26 REAL,
            ema30 REAL,
            ema60 REAL,
            macd REAL,
            macd_signal REAL,
            macd_histogram REAL,
            rsi6 REAL,
            rsi12 REAL,
            rsi24 REAL,
            kdj_k REAL,
            kdj_d REAL,
            kdj_j REAL,
            cci REAL,
            williams_r REAL,
            stoch_k REAL,
            stoch_d REAL,
            bollinger_upper REAL,
            bollinger_middle REAL,
            bollinger_lower REAL,

            -- 资金流向 (20个字段)
            main_inflow REAL,
            main_outflow REAL,
            main_net_inflow REAL,
            main_net_inflow_rate REAL,
            super_large_inflow REAL,
            super_large_outflow REAL,
            super_large_net_inflow REAL,
            large_inflow REAL,
            large_outflow REAL,
            large_net_inflow REAL,
            medium_inflow REAL,
            medium_outflow REAL,
            medium_net_inflow REAL,
            small_inflow REAL,
            small_outflow REAL,
            small_net_inflow REAL,
            retail_inflow REAL,
            retail_outflow REAL,
            retail_net_inflow REAL,
            institutional_net_inflow REAL,

            -- 财务数据 (25个字段)
            total_revenue REAL,
            total_revenue_ttm REAL,
            operating_revenue REAL,
            net_profit REAL,
            net_profit_ttm REAL,
            net_profit_yoy REAL,
            operating_profit REAL,
            ebitda REAL,
            total_assets REAL,
            total_liabilities REAL,
            current_assets REAL,
            current_liabilities REAL,
            fixed_assets REAL,
            intangible_assets REAL,
            shareholders_equity REAL,
            retained_earnings REAL,
            operating_cash_flow REAL,
            investing_cash_flow REAL,
            financing_cash_flow REAL,
            free_cash_flow REAL,
            working_capital REAL,
            inventory REAL,
            accounts_receivable REAL,
            accounts_payable REAL,
            goodwill REAL,

            -- 行业对比 (10个字段)
            industry_pe_avg REAL,
            industry_pb_avg REAL,
            industry_roe_avg REAL,
            industry_revenue_growth_avg REAL,
            industry_profit_growth_avg REAL,
            industry_rank INTEGER,
            industry_percentile REAL,
            sector_rank INTEGER,
            sector_percentile REAL,
            market_rank INTEGER,

            -- 机构数据 (15个字段)
            institution_holding_ratio REAL,
            institution_holding_change REAL,
            fund_holding_count INTEGER,
            fund_holding_ratio REAL,
            fund_holding_change REAL,
            qfii_holding_ratio REAL,
            qfii_holding_change REAL,
            social_security_holding REAL,
            insurance_holding REAL,
            broker_holding REAL,
            trust_holding REAL,
            private_equity_holding REAL,
            corporate_holding REAL,
            individual_holding REAL,
            top10_shareholders_ratio REAL,

            -- 估值指标 (10个字段)
            enterprise_value REAL,
            ev_revenue REAL,
            ev_ebitda REAL,
            price_to_sales REAL,
            price_to_book REAL,
            price_to_cash_flow REAL,
            dividend_yield REAL,
            dividend_per_share REAL,
            payout_ratio REAL,
            retention_ratio REAL,

            -- 价格相关指标 (缺失的字段)
            amplitude REAL,
            speed REAL,
            five_minute_change REAL,
            sixty_minute_change REAL,
            today_open REAL,
            yesterday_close REAL,

            -- 增强收集器字段
            secid TEXT,
            data_timestamp TEXT,

            -- 时间戳
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # 历史数据表 - 扩展字段
        historical_data_schema = """
        CREATE TABLE daily_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stock_code TEXT NOT NULL,
            trade_date DATE NOT NULL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            close_price REAL,
            pre_close REAL,
            volume REAL,
            turnover REAL,
            change_amount REAL,
            change_percent REAL,
            turnover_rate REAL,
            amplitude REAL,

            -- 技术指标
            ma5 REAL,
            ma10 REAL,
            ma20 REAL,
            ma30 REAL,
            ma60 REAL,
            ma120 REAL,
            ma250 REAL,
            ema12 REAL,
            ema26 REAL,
            macd REAL,
            macd_signal REAL,
            macd_histogram REAL,
            rsi6 REAL,
            rsi12 REAL,
            rsi24 REAL,
            kdj_k REAL,
            kdj_d REAL,
            kdj_j REAL,

            -- 资金流向
            main_net_inflow REAL,
            main_inflow REAL,
            main_outflow REAL,

            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(stock_code, trade_date)
        )
        """
        
        # 实时数据表
        realtime_data_schema = """
        CREATE TABLE IF NOT EXISTS realtime_quotes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stock_code TEXT NOT NULL,
            current_price REAL,
            change_amount REAL,
            change_percent REAL,
            volume REAL,
            turnover REAL,
            high_price REAL,
            low_price REAL,
            open_price REAL,
            pre_close REAL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # 创建表
        for db_path, schemas in [
            (self.stock_db_path, [stock_info_schema]),
            (self.daily_db_path, [historical_data_schema]),
            (self.realtime_db_path, [realtime_data_schema])
        ]:
            conn = sqlite3.connect(db_path)
            for schema in schemas:
                conn.execute(schema)
            conn.commit()
            conn.close()
            
        logger.info("✅ 数据库表结构创建完成")
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取A股全部股票列表"""
        logger.info("📋 获取A股全部股票列表...")

        all_stocks = []

        # 获取沪深A股全部股票
        markets = [
            {'name': '沪A', 'fs': 'm:1+t:2,m:1+t:23'},  # 上交所A股
            {'name': '深A', 'fs': 'm:0+t:6,m:0+t:80'},  # 深交所A股
            {'name': '创业板', 'fs': 'm:0+t:80'},        # 创业板
            {'name': '科创板', 'fs': 'm:1+t:23'},        # 科创板
        ]

        for market in markets:
            try:
                logger.info(f"📊 获取{market['name']}股票...")

                # 分页获取，每页1000只
                page = 1
                while True:
                    url = "https://push2.eastmoney.com/api/qt/clist/get"

                    async with aiohttp.ClientSession() as session:
                        params = {
                            'pn': str(page),
                            'pz': '1000',
                            'po': '1',
                            'np': '1',
                            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                            'fltt': '2',
                            'invt': '2',
                            'fid': 'f3',
                            'fs': market['fs'],
                            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
                        }

                        async with session.get(url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()

                                if data.get('data') and data['data'].get('diff'):
                                    page_stocks = []
                                    for item in data['data']['diff']:
                                        stock = {
                                            'stock_code': item.get('f12', ''),
                                            'stock_name': item.get('f14', ''),
                                            'current_price': item.get('f2', 0),
                                            'market': market['name'],
                                            'exchange': '深交所' if item.get('f13', 0) == 0 else '上交所'
                                        }
                                        if stock['stock_code'] and len(stock['stock_code']) == 6:
                                            page_stocks.append(stock)

                                    all_stocks.extend(page_stocks)
                                    logger.info(f"📈 {market['name']}第{page}页: {len(page_stocks)}只股票")

                                    # 如果这页少于1000只，说明已经是最后一页
                                    if len(page_stocks) < 1000:
                                        break

                                    page += 1
                                    await asyncio.sleep(0.1)  # 避免请求过快
                                else:
                                    break
                            else:
                                logger.error(f"❌ 获取{market['name']}股票失败: HTTP {response.status}")
                                break

            except Exception as e:
                logger.error(f"❌ 获取{market['name']}股票异常: {e}")
                continue

        # 去重
        unique_stocks = {}
        for stock in all_stocks:
            code = stock['stock_code']
            if code not in unique_stocks:
                unique_stocks[code] = stock

        final_stocks = list(unique_stocks.values())
        logger.info(f"✅ 获取到A股全部股票: {len(final_stocks)} 只")

        return final_stocks

    async def collect_stock_basic_info(self, stock_list: List[Dict[str, Any]]):
        """收集股票完整信息（基础+财务+技术指标）"""
        logger.info("📊 开始收集股票完整信息...")

        conn = sqlite3.connect(self.stock_db_path)
        total_stocks = len(stock_list)

        for i, stock in enumerate(stock_list):
            try:
                stock_code = stock['stock_code']
                logger.info(f"📈 收集完整信息: {stock_code} ({i+1}/{total_stocks})")

                # 1. 获取增强版股票信息（包含所有东财字段）
                if self.enhanced_collector:
                    stock_info = await self.enhanced_collector.get_comprehensive_stock_data(stock_code)
                else:
                    stock_info = await self.get_detailed_stock_info(stock_code)

                # 2. 获取财务数据（暂时禁用，因为API格式问题）
                # financial_data = await self.financial_calculator.get_financial_data(stock_code)
                # if financial_data:
                #     stock_info.update(financial_data)
                #
                #     # 计算财务比率
                #     financial_ratios = self.financial_calculator.calculate_financial_ratios(financial_data)
                #     stock_info.update(financial_ratios)

                # 3. 获取历史数据用于技术指标计算
                historical_data = await self.get_historical_data(stock_code, days=250)
                if historical_data and len(historical_data) >= 250:
                    # 转换为DataFrame
                    df = pd.DataFrame(historical_data)
                    df = df.sort_values('trade_date')

                    # 计算技术指标
                    technical_indicators = self.technical_calculator.calculate_all_indicators(df)
                    stock_info.update(technical_indicators)

                    # 计算成交量指标
                    volume_indicators = self.technical_calculator.calculate_volume_indicators(df)
                    stock_info.update(volume_indicators)

                    # 计算价格指标
                    price_indicators = self.technical_calculator.calculate_price_indicators(df)
                    stock_info.update(price_indicators)

                if stock_info:
                    # 插入数据库
                    await self.insert_stock_info(conn, stock_info)
                    logger.info(f"✅ {stock_code}: 完整信息已保存")
                else:
                    logger.warning(f"⚠️ {stock_code}: 无法获取信息")

                # 避免API限制
                await asyncio.sleep(0.2)

                # 每100只股票提交一次
                if (i + 1) % 100 == 0:
                    conn.commit()
                    logger.info(f"💾 已提交{i+1}只股票的完整信息")

            except Exception as e:
                logger.error(f"❌ 收集股票 {stock.get('stock_code')} 完整信息失败: {e}")
                continue

        conn.commit()
        conn.close()
        logger.info("✅ 股票完整信息收集完成")

    async def get_detailed_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取详细股票信息 - 130+字段"""
        try:
            # 确定交易所代码
            if stock_code.startswith(('000', '001', '002', '003', '300', '301')):
                secid = f"0.{stock_code}"
            else:
                secid = f"1.{stock_code}"

            # 东财详细信息API
            url = "https://push2.eastmoney.com/api/qt/stock/get"

            async with aiohttp.ClientSession() as session:
                params = {
                    'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                    'invt': '2',
                    'fltt': '1',
                    'secid': secid,
                    'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25,f26,f27,f28,f29,f30,f31,f32,f33,f34,f35,f36,f37,f38,f39,f40,f41,f42,f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65,f66,f67,f68,f69,f70,f71,f72,f73,f74,f75,f76,f77,f78,f79,f80,f81,f82,f83,f84,f85,f86,f87,f88,f89,f90,f91,f92,f93,f94,f95,f96,f97,f98,f99,f100,f101,f102,f103,f104,f105,f106,f107,f108,f109,f110,f111,f112,f113,f114,f115,f116,f117,f118,f119,f120,f121,f122,f123,f124,f125,f126,f127,f128,f129,f130,f131,f132,f133,f134,f135,f136,f137,f138,f139,f140,f141,f142,f143,f144,f145,f146,f147,f148,f149,f150,f151,f152,f153,f154,f155,f156,f157,f158,f159,f160,f161,f162,f163,f164,f165,f166,f167,f168,f169,f170'
                }

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get('data'):
                            stock_data = data['data']

                            # 解析所有字段
                            info = {
                                'stock_code': stock_code,
                                'stock_name': stock_data.get('f58', ''),
                                'current_price': stock_data.get('f43', 0),
                                'open_price': stock_data.get('f46', 0),
                                'high_price': stock_data.get('f44', 0),
                                'low_price': stock_data.get('f45', 0),
                                'close_price': stock_data.get('f60', 0),
                                'pre_close': stock_data.get('f60', 0),
                                'change_amount': stock_data.get('f169', 0),
                                'change_percent': stock_data.get('f170', 0),
                                'volume': stock_data.get('f47', 0),
                                'turnover': stock_data.get('f48', 0),
                                'turnover_rate': stock_data.get('f168', 0),
                                'market_cap': stock_data.get('f116', 0),
                                'circulating_market_cap': stock_data.get('f117', 0),
                                'pe_ratio': stock_data.get('f114', 0),
                                'pb_ratio': stock_data.get('f115', 0),
                                'amplitude': stock_data.get('f120', 0),
                                'volume_ratio': stock_data.get('f111', 0),
                                'market': '深交所' if secid.startswith('0.') else '上交所'
                            }

                            return info

            return None

        except Exception as e:
            logger.error(f"❌ 获取股票 {stock_code} 详细信息失败: {e}")
            return None

    async def insert_stock_info(self, conn: sqlite3.Connection, stock_info: Dict[str, Any]):
        """插入股票信息到数据库"""
        try:
            cursor = conn.cursor()

            # 获取表的所有列名
            cursor.execute("PRAGMA table_info(stock_info)")
            table_columns = [row[1] for row in cursor.fetchall()]

            # 过滤掉不存在的字段
            filtered_data = {}
            for key, value in stock_info.items():
                if key in table_columns:
                    filtered_data[key] = value
                else:
                    logger.debug(f"跳过不存在的字段: {key}")

            if not filtered_data:
                logger.warning("没有有效字段可插入")
                return

            # 构建插入语句
            columns = list(filtered_data.keys())
            placeholders = ','.join(['?' for _ in columns])
            values = list(filtered_data.values())

            sql = f"""
                INSERT OR REPLACE INTO stock_info ({','.join(columns)})
                VALUES ({placeholders})
            """

            cursor.execute(sql, values)
            conn.commit()

        except Exception as e:
            logger.error(f"❌ 插入股票信息失败: {e}")

    async def collect_historical_data(self, stock_list: List[Dict[str, Any]]):
        """收集10年历史数据"""
        logger.info("📈 开始收集10年历史数据...")

        conn = sqlite3.connect(self.daily_db_path)
        total_stocks = len(stock_list)

        # 分批处理，每批100只股票
        batch_size = 100
        for batch_start in range(0, total_stocks, batch_size):
            batch_end = min(batch_start + batch_size, total_stocks)
            batch = stock_list[batch_start:batch_end]

            logger.info(f"📊 处理第{batch_start//batch_size + 1}批股票 ({batch_start+1}-{batch_end}/{total_stocks})")

            for i, stock in enumerate(batch):
                try:
                    stock_code = stock['stock_code']
                    current_index = batch_start + i + 1
                    logger.info(f"📈 收集历史数据: {stock_code} ({current_index}/{total_stocks})")

                    # 获取10年历史数据（使用增强收集器）
                    if self.enhanced_collector:
                        historical_data = await self.enhanced_collector.get_historical_comprehensive_data(stock_code, days=2500)
                    else:
                        historical_data = await self.get_historical_data(stock_code, days=2500)  # 10年约2500个交易日

                    if historical_data:
                        # 插入数据库
                        await self.insert_historical_data(conn, stock_code, historical_data)
                        logger.info(f"✅ {stock_code}: {len(historical_data)}条历史记录")
                    else:
                        logger.warning(f"⚠️ {stock_code}: 无历史数据")

                    # 避免API限制
                    await asyncio.sleep(0.1)

                    # 每100只股票提交一次
                    if (current_index) % 100 == 0:
                        conn.commit()
                        logger.info(f"💾 已提交{current_index}只股票的数据")

                except Exception as e:
                    logger.error(f"❌ 收集股票 {stock.get('stock_code')} 历史数据失败: {e}")
                    continue

            # 批次间休息
            await asyncio.sleep(1)

        conn.commit()
        conn.close()
        logger.info("✅ 10年历史数据收集完成")

    async def get_historical_data(self, stock_code: str, days: int = 250) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            # 确定交易所代码
            if stock_code.startswith(('000', '001', '002', '003', '300', '301')):
                secid = f"0.{stock_code}"
            else:
                secid = f"1.{stock_code}"

            # 东财历史数据API
            url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"

            async with aiohttp.ClientSession() as session:
                params = {
                    'secid': secid,
                    'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                    'fields1': 'f1,f2,f3,f4,f5,f6',
                    'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                    'klt': '101',  # 日K线
                    'fqt': '1',    # 前复权
                    'end': '20500101',
                    'lmt': str(days)
                }

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get('data') and data['data'].get('klines'):
                            klines = data['data']['klines']
                            historical_data = []

                            for kline in klines:
                                parts = kline.split(',')
                                if len(parts) >= 11:
                                    data_point = {
                                        'trade_date': parts[0],
                                        'open_price': float(parts[1]),
                                        'close_price': float(parts[2]),
                                        'high_price': float(parts[3]),
                                        'low_price': float(parts[4]),
                                        'volume': float(parts[5]),
                                        'turnover': float(parts[6]),
                                        'change_amount': float(parts[7]),
                                        'change_percent': float(parts[8]),
                                        'turnover_rate': float(parts[10]) if len(parts) > 10 else 0
                                    }
                                    historical_data.append(data_point)

                            return historical_data

            return []

        except Exception as e:
            logger.error(f"❌ 获取股票 {stock_code} 历史数据失败: {e}")
            return []

    async def insert_historical_data(self, conn: sqlite3.Connection, stock_code: str, historical_data: List[Dict[str, Any]]):
        """插入历史数据到数据库"""
        try:
            cursor = conn.cursor()

            for data_point in historical_data:
                data_point['stock_code'] = stock_code

                columns = list(data_point.keys())
                placeholders = ','.join(['?' for _ in columns])
                values = list(data_point.values())

                sql = f"""
                    INSERT OR REPLACE INTO daily_data ({','.join(columns)})
                    VALUES ({placeholders})
                """

                cursor.execute(sql, values)

            conn.commit()

        except Exception as e:
            logger.error(f"❌ 插入历史数据失败: {e}")

    async def collect_realtime_data(self, stock_list: List[Dict[str, Any]]):
        """收集实时数据"""
        logger.info("⚡ 开始收集实时数据...")

        conn = sqlite3.connect(self.realtime_db_path)

        # 批量获取实时数据
        batch_size = 50
        for i in range(0, min(len(stock_list), 100), batch_size):  # 先收集前100只
            batch = stock_list[i:i+batch_size]

            try:
                logger.info(f"⚡ 收集实时数据批次: {i//batch_size + 1}")

                realtime_data = await self.get_batch_realtime_data(batch)

                if realtime_data:
                    await self.insert_realtime_data(conn, realtime_data)

                # 避免API限制
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"❌ 收集实时数据批次失败: {e}")
                continue

        conn.close()
        logger.info("✅ 实时数据收集完成")

    async def get_batch_realtime_data(self, stock_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量获取实时数据"""
        try:
            # 构建secids
            secids = []
            for stock in stock_batch:
                stock_code = stock['stock_code']
                if stock_code.startswith(('000', '001', '002', '003', '300', '301')):
                    secids.append(f"0.{stock_code}")
                else:
                    secids.append(f"1.{stock_code}")

            secids_str = ','.join(secids)

            # 东财实时数据API
            url = "https://push2.eastmoney.com/api/qt/ulist/get"

            async with aiohttp.ClientSession() as session:
                params = {
                    'fltt': '1',
                    'invt': '2',
                    'fields': 'f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21',
                    'secids': secids_str,
                    'ut': 'fa5fd1943c7b386f172d6893dbfba10b'
                }

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get('data') and data['data'].get('diff'):
                            realtime_data = []

                            for item in data['data']['diff']:
                                data_point = {
                                    'stock_code': item.get('f12', ''),
                                    'current_price': item.get('f2', 0),
                                    'change_amount': item.get('f4', 0),
                                    'change_percent': item.get('f3', 0),
                                    'volume': item.get('f5', 0),
                                    'turnover': item.get('f6', 0),
                                    'high_price': item.get('f15', 0),
                                    'low_price': item.get('f16', 0),
                                    'open_price': item.get('f17', 0),
                                    'pre_close': item.get('f18', 0)
                                }
                                realtime_data.append(data_point)

                            return realtime_data

            return []

        except Exception as e:
            logger.error(f"❌ 批量获取实时数据失败: {e}")
            return []

    async def insert_realtime_data(self, conn: sqlite3.Connection, realtime_data: List[Dict[str, Any]]):
        """插入实时数据到数据库"""
        try:
            cursor = conn.cursor()

            for data_point in realtime_data:
                columns = list(data_point.keys())
                placeholders = ','.join(['?' for _ in columns])
                values = list(data_point.values())

                sql = f"""
                    INSERT INTO realtime_quotes ({','.join(columns)})
                    VALUES ({placeholders})
                """

                cursor.execute(sql, values)

            conn.commit()

        except Exception as e:
            logger.error(f"❌ 插入实时数据失败: {e}")

async def main():
    """主函数"""
    rebuilder = StockDatabaseRebuilder()
    await rebuilder.rebuild_all_databases()

if __name__ == "__main__":
    asyncio.run(main())
