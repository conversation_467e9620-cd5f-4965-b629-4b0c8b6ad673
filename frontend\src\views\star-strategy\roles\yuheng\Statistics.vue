<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/玉衡.png" alt="玉衡星" />
          </div>
          <div class="role-details">
            <h1>玉衡星 - 交易执行统计中心</h1>
            <p>交易执行统计、订单管理分析与执行效果评估</p>
          </div>
        </div>
        <div class="header-actions">
          <el-select v-model="selectedPeriod" @change="loadStatistics" placeholder="选择周期" size="default">
            <el-option label="日统计" value="daily" />
            <el-option label="周统计" value="weekly" />
            <el-option label="月统计" value="monthly" />
          </el-select>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 - 增强版 -->
    <div class="metrics-grid" v-loading="loading">
      <div class="metric-card primary enhanced" @click="showMetricDetail('trades')">
        <div class="metric-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value animated-number">{{ coreMetrics.totalTrades }}</div>
          <div class="metric-label">总交易笔数</div>
          <div class="metric-change" :class="coreMetrics.tradesTrend >= 0 ? 'positive' : 'negative'">
            <el-icon><ArrowUp v-if="coreMetrics.tradesTrend >= 0" /><ArrowDown v-else /></el-icon>
            {{ coreMetrics.tradesTrend >= 0 ? '+' : '' }}{{ coreMetrics.tradesTrend }}%
          </div>
        </div>
        <div class="metric-sparkline">
          <div class="sparkline-bar" v-for="(value, index) in sparklineData.trades" :key="index"
               :style="{ height: value + '%' }"></div>
        </div>
      </div>

      <div class="metric-card success enhanced" @click="showMetricDetail('success')">
        <div class="metric-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value animated-number">{{ (coreMetrics.successRate * 100).toFixed(1) }}%</div>
          <div class="metric-label">执行成功率</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.successTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.avgExecutionTime }}ms</div>
          <div class="metric-label">平均执行时间</div>
          <div class="metric-change negative">
            <el-icon><ArrowDown /></el-icon>
            -{{ coreMetrics.timeTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card info">
        <div class="metric-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">¥{{ formatNumber(coreMetrics.totalVolume) }}</div>
          <div class="metric-label">总交易金额</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.volumeTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 交易执行趋势图 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>交易执行趋势</h3>
              <div class="chart-controls">
                <el-button-group>
                  <el-button
                    v-for="period in trendPeriods"
                    :key="period.value"
                    :type="selectedTrendPeriod === period.value ? 'primary' : 'default'"
                    @click="changeTrendPeriod(period.value)"
                    size="small"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <v-chart :option="executionTrendOption" style="height: 300px;" />
          </div>
        </el-card>
      </div>

      <!-- 执行算法分布 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>执行算法分布</h3>
              <el-tooltip content="显示不同执行算法的使用情况" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="chart-content">
            <v-chart :option="algorithmDistributionOption" style="height: 300px;" />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 详细统计表格 -->
    <div class="table-container">
      <el-card>
        <template #header>
          <div class="table-header">
            <h3>交易执行详细统计</h3>
            <div class="table-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索股票代码或名称"
                prefix-icon="Search"
                size="default"
                style="width: 200px; margin-right: 10px;"
              />
              <el-button @click="exportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredStatistics"
          v-loading="loading"
          stripe
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="stock_code" label="股票代码" width="100" sortable />
          <el-table-column prop="stock_name" label="股票名称" width="120" />
          <el-table-column prop="total_trades" label="交易笔数" width="100" sortable />
          <el-table-column prop="success_rate" label="成功率" width="100" sortable>
            <template #default="{ row }">
              <el-tag :type="getSuccessRateType(row.success_rate)">
                {{ (row.success_rate * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="avg_execution_time" label="平均执行时间" width="120" sortable>
            <template #default="{ row }">
              {{ row.avg_execution_time }}ms
            </template>
          </el-table-column>
          <el-table-column prop="total_volume" label="交易金额" width="120" sortable>
            <template #default="{ row }">
              ¥{{ formatNumber(row.total_volume) }}
            </template>
          </el-table-column>
          <el-table-column prop="slippage" label="滑点" width="100" sortable>
            <template #default="{ row }">
              <span :class="{ 'text-red': row.slippage > 0.01, 'text-green': row.slippage <= 0.005 }">
                {{ (row.slippage * 100).toFixed(3) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="algorithm" label="主要算法" width="120" />
          <el-table-column prop="last_trade_time" label="最后交易时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.last_trade_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="viewDetails(row)">详情</el-button>
              <el-button type="text" @click="viewTrades(row)">交易记录</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalRecords"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 实时监控面板 -->
    <div class="monitoring-panel">
      <el-card>
        <template #header>
          <div class="panel-header">
            <h3>实时执行监控</h3>
            <div class="status-indicator" :class="{ active: isRealTimeActive }">
              <span class="dot"></span>
              <span>{{ isRealTimeActive ? '实时监控中' : '监控已停止' }}</span>
            </div>
          </div>
        </template>

        <div class="monitoring-content">
          <div class="monitor-metrics">
            <div class="monitor-item">
              <div class="monitor-label">当前执行订单</div>
              <div class="monitor-value">{{ realTimeData.activeOrders }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">等待队列</div>
              <div class="monitor-value">{{ realTimeData.pendingOrders }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">今日成交</div>
              <div class="monitor-value">{{ realTimeData.todayTrades }}</div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">系统负载</div>
              <div class="monitor-value">{{ realTimeData.systemLoad }}%</div>
            </div>
          </div>

          <div class="recent-trades">
            <h4>最近交易</h4>
            <div class="trade-list">
              <div
                v-for="trade in realTimeData.recentTrades"
                :key="trade.id"
                class="trade-item"
              >
                <div class="trade-info">
                  <span class="stock-code">{{ trade.stock_code }}</span>
                  <span class="trade-type" :class="trade.side">{{ trade.side === 'buy' ? '买入' : '卖出' }}</span>
                  <span class="trade-quantity">{{ trade.quantity }}股</span>
                  <span class="trade-price">¥{{ trade.price }}</span>
                </div>
                <div class="trade-time">{{ formatTime(trade.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { YuhengStarAPI } from '@/api/roles/yuheng-star'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const loading = ref(false)
const selectedPeriod = ref('daily')
const selectedTrendPeriod = ref('7d')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)
const isRealTimeActive = ref(true)

// 核心指标数据
const coreMetrics = reactive({
  totalTrades: 1247,
  tradesTrend: 12.5,
  successRate: 0.987,
  successTrend: 2.3,
  avgExecutionTime: 156,
  timeTrend: 8.7,
  totalVolume: 125670000,
  volumeTrend: 15.2
})

// 趋势周期选项
const trendPeriods = [
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// 统计数据
const statistics = ref([])
const realTimeData = reactive({
  activeOrders: 23,
  pendingOrders: 8,
  todayTrades: 156,
  systemLoad: 67,
  recentTrades: []
})

// 计算属性
const filteredStatistics = computed(() => {
  if (!searchKeyword.value) return statistics.value
  return statistics.value.filter(item =>
    item.stock_code.includes(searchKeyword.value) ||
    item.stock_name.includes(searchKeyword.value)
  )
})

// 图表配置
const executionTrendOption = computed(() => ({
  title: {
    text: '交易执行趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['成功交易', '失败交易', '执行时间'],
    bottom: 0
  },
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: [
    {
      type: 'value',
      name: '交易笔数'
    },
    {
      type: 'value',
      name: '执行时间(ms)',
      position: 'right'
    }
  ],
  series: [
    {
      name: '成功交易',
      type: 'bar',
      data: [], // 将通过真实数据动态填充
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '失败交易',
      type: 'bar',
      data: [], // 将通过真实数据动态填充
      itemStyle: { color: '#F56C6C' }
    },
    {
      name: '执行时间',
      type: 'line',
      yAxisIndex: 1,
      data: [], // 将通过真实数据动态填充
      itemStyle: { color: '#409EFF' }
    }
  ]
}))

const algorithmDistributionOption = computed(() => ({
  title: {
    text: '执行算法分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '执行算法',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 335, name: 'TWAP' },
        { value: 310, name: 'VWAP' },
        { value: 234, name: 'POV' },
        { value: 135, name: 'IS' },
        { value: 148, name: 'MOC' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    // 调用真实API加载统计数据
    const statsResponse = await YuhengStarAPI.getExecutionStatistics(selectedPeriod.value)

    if (statsResponse.success && statsResponse.data) {
      // 更新统计数据
      statistics.value = statsResponse.data.statistics || []
      totalRecords.value = statsResponse.data.total || 0

      // 更新核心指标
      if (statsResponse.data.metrics) {
        Object.assign(coreMetrics, statsResponse.data.metrics)
      }
    }

    // 加载任务统计
    const taskStatsResponse = await YuhengStarAPI.getTaskStatistics(selectedPeriod.value)
    if (taskStatsResponse.success && taskStatsResponse.data) {
      // 更新任务相关统计
      console.log('任务统计数据:', taskStatsResponse.data)
    }

  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadStatistics()
  loadRealTimeData()
}

const loadRealTimeData = async () => {
  try {
    // 获取实时交易状态
    const statusResponse = await YuhengStarAPI.getRealTimeStatus()
    if (statusResponse.success && statusResponse.data) {
      Object.assign(realTimeData, statusResponse.data)
    }

    // 获取最近交易记录
    const recentTradesResponse = await YuhengStarAPI.getOrders({
      limit: 10,
      status: 'filled'
    })
    if (recentTradesResponse.success && recentTradesResponse.data) {
      realTimeData.recentTrades = recentTradesResponse.data.orders || []
    }

  } catch (error) {
    console.error('加载实时数据失败:', error)
  }
}

const changeTrendPeriod = (period: string) => {
  selectedTrendPeriod.value = period
  // 重新加载趋势数据
}

const getSuccessRateType = (rate: number) => {
  if (rate >= 0.95) return 'success'
  if (rate >= 0.90) return 'warning'
  return 'danger'
}

const formatNumber = (num: number) => {
  if (num >= 100000000) return (num / 100000000).toFixed(1) + '亿'
  if (num >= 10000) return (num / 10000).toFixed(1) + '万'
  return num.toString()
}

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const handleSortChange = ({ column, prop, order }) => {
  // 处理排序
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadStatistics()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadStatistics()
}

const exportData = () => {
  // 导出数据逻辑
  ElMessage.success('数据导出中...')
}

const viewDetails = (row: any) => {
  // 查看详情逻辑
}

const viewTrades = (row: any) => {
  // 查看交易详情
}

// 增强功能：指标详情显示
const showMetricDetail = (metricType: string) => {
  ElMessage.info(`显示${metricType}详细信息`)
  // 这里可以打开详情弹窗或跳转到详情页面
}

// 增强功能：模拟火花线数据
const sparklineData = reactive({
  trades: [20, 35, 45, 25, 60, 80, 55, 70, 90, 65],
  success: [80, 85, 90, 88, 92, 95, 93, 96, 94, 97],
  volume: [30, 45, 60, 40, 70, 85, 75, 90, 80, 95],
  latency: [15, 12, 18, 10, 8, 12, 9, 11, 7, 10]
})

// 增强功能：实时数据更新
const startRealTimeUpdates = () => {
  setInterval(async () => {
    if (!loading.value) {
      await loadRealTimeData()
      // 更新火花线数据
      updateSparklineData()
    }
  }, 30000) // 每30秒更新一次
}

const updateSparklineData = () => {
  // 模拟数据更新
  Object.keys(sparklineData).forEach(key => {
    sparklineData[key].shift()
    sparklineData[key].push(Math.floor(Math.random() * 100))
  })
}
  // 查看交易记录逻辑
}

// 生命周期
onMounted(() => {
  loadStatistics()
  loadRealTimeData()

  // 启动实时数据更新
  const interval = setInterval(loadRealTimeData, 5000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style lang="scss" scoped>
@import '@/styles/role-page.scss';

.monitoring-panel {
  margin-top: 20px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ccc;

        .active & {
          background: #67C23A;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .monitoring-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .monitor-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;

      .monitor-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;

        .monitor-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 5px;
        }

        .monitor-value {
          font-size: 24px;
          font-weight: bold;
          color: #409EFF;
        }
      }
    }

    .recent-trades {
      .trade-list {
        max-height: 200px;
        overflow-y: auto;

        .trade-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #eee;

          .trade-info {
            display: flex;
            gap: 10px;
            align-items: center;

            .stock-code {
              font-weight: bold;
            }

            .trade-type {
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 12px;

              &.buy {
                background: #f0f9ff;
                color: #1890ff;
              }

              &.sell {
                background: #fff2f0;
                color: #ff4d4f;
              }
            }
          }

          .trade-time {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
}

.text-red {
  color: #F56C6C;
}

.text-green {
  color: #67C23A;
}

// 增强的动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 增强的指标卡片样式
.metric-card.enhanced {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover:before {
    left: 100%;
  }

  .animated-number {
    animation: countUp 0.6s ease-out;
  }

  .metric-sparkline {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 30px;
    display: flex;
    align-items: end;
    gap: 2px;
    padding: 5px;
    opacity: 0.3;

    .sparkline-bar {
      flex: 1;
      background: currentColor;
      border-radius: 1px;
      min-height: 2px;
      transition: height 0.3s ease;
    }
  }
}

// 响应式设计增强
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .metric-card {
    .metric-value {
      font-size: 28px;
    }
  }

  .statistics-table {
    .el-table {
      font-size: 12px;
    }
  }
}

// 加载状态优化
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

// 数据可视化增强
.chart-container {
  position: relative;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
    border-radius: 8px;
    pointer-events: none;
  }
}
</style>
