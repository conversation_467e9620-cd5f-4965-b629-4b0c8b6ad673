"""
增强的聊天API - 深度集成量化交易业务
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import asyncio
from datetime import datetime

from services.enhanced_chat_service import (
    EnhancedQuantChatService, 
    ChatContext, 
    EnhancedChatResponse,
    ExecutableAction
)

router = APIRouter(prefix="/api/enhanced-chat", tags=["Enhanced Chat"])

class EnhancedChatRequest(BaseModel):
    """增强的聊天请求"""
    message: str
    user_id: str
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    execute_actions: bool = False  # 是否自动执行建议的操作

class EnhancedChatResponseModel(BaseModel):
    """增强的聊天响应模型"""
    success: bool
    message: str
    data: Dict[str, Any]
    timestamp: str

# 全局服务实例
chat_service = EnhancedQuantChatService()

@router.post("/chat", response_model=EnhancedChatResponseModel)
async def enhanced_chat(request: EnhancedChatRequest):
    """
    增强的聊天接口
    - 智能意图识别
    - 业务数据集成
    - 可执行建议生成
    - 风险评估
    """
    try:
        # 构建聊天上下文
        context = ChatContext(
            user_id=request.user_id,
            session_id=request.session_id or f"session_{datetime.now().timestamp()}",
            portfolio_data=request.context.get("portfolio") if request.context else None,
            market_context=request.context.get("market") if request.context else None,
            recent_trades=request.context.get("recent_trades") if request.context else None,
            risk_profile=request.context.get("risk_profile") if request.context else None
        )
        
        # 处理聊天
        response = await chat_service.process_chat(request.message, context)
        
        # 如果用户选择自动执行
        execution_results = []
        if request.execute_actions and response.executable_actions:
            execution_results = await execute_suggested_actions(
                response.executable_actions, 
                context
            )
        
        return EnhancedChatResponseModel(
            success=True,
            message="聊天处理成功",
            data={
                "text_response": response.text_response,
                "role_responses": response.role_responses,
                "data_visualizations": response.data_visualizations,
                "executable_actions": [
                    {
                        "type": action.type,
                        "parameters": action.parameters,
                        "confidence": action.confidence,
                        "risk_level": action.risk_level,
                        "estimated_impact": action.estimated_impact
                    }
                    for action in response.executable_actions
                ],
                "risk_warnings": response.risk_warnings,
                "follow_up_suggestions": response.follow_up_suggestions,
                "confidence_score": response.confidence_score,
                "processing_time": response.processing_time,
                "execution_results": execution_results
            },
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"聊天处理失败: {str(e)}")

@router.get("/context/{user_id}")
async def get_user_context(user_id: str):
    """获取用户上下文信息"""
    try:
        # 这里应该从数据库或缓存中获取用户上下文
        context = {
            "portfolio": await get_user_portfolio(user_id),
            "recent_trades": await get_recent_trades(user_id),
            "risk_profile": await get_risk_profile(user_id),
            "preferences": await get_user_preferences(user_id)
        }
        
        return {
            "success": True,
            "data": context,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户上下文失败: {str(e)}")

@router.post("/execute-action")
async def execute_action(action_data: Dict[str, Any], user_id: str):
    """执行单个操作"""
    try:
        action = ExecutableAction(
            type=action_data["type"],
            parameters=action_data["parameters"],
            confidence=action_data.get("confidence", 0.5),
            risk_level=action_data.get("risk_level", "medium"),
            estimated_impact=action_data.get("estimated_impact", "")
        )
        
        context = ChatContext(user_id=user_id, session_id="manual_execution")
        result = await execute_single_action(action, context)
        
        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行操作失败: {str(e)}")

@router.get("/market-insights")
async def get_market_insights():
    """获取市场洞察"""
    try:
        insights = {
            "market_summary": await get_market_summary(),
            "top_movers": await get_top_movers(),
            "sector_rotation": await get_sector_rotation(),
            "sentiment_analysis": await get_sentiment_analysis(),
            "technical_signals": await get_technical_signals()
        }
        
        return {
            "success": True,
            "data": insights,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场洞察失败: {str(e)}")

@router.get("/smart-suggestions/{user_id}")
async def get_smart_suggestions(user_id: str):
    """获取智能建议"""
    try:
        # 基于用户画像和市场状况生成智能建议
        suggestions = await generate_smart_suggestions(user_id)
        
        return {
            "success": True,
            "data": {
                "suggestions": suggestions,
                "reasoning": "基于您的投资偏好和当前市场状况",
                "confidence": 0.82
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取智能建议失败: {str(e)}")

# 辅助函数
async def execute_suggested_actions(actions: List[ExecutableAction], context: ChatContext) -> List[Dict]:
    """执行建议的操作"""
    results = []
    
    for action in actions:
        try:
            result = await execute_single_action(action, context)
            results.append({
                "action": action.type,
                "status": "success",
                "result": result
            })
        except Exception as e:
            results.append({
                "action": action.type,
                "status": "failed",
                "error": str(e)
            })
    
    return results

async def execute_single_action(action: ExecutableAction, context: ChatContext) -> Dict:
    """执行单个操作"""
    if action.type == "buy_order":
        # 调用交易系统执行买入订单
        return await place_buy_order(action.parameters, context.user_id)
    
    elif action.type == "sell_order":
        # 调用交易系统执行卖出订单
        return await place_sell_order(action.parameters, context.user_id)
    
    elif action.type == "set_alert":
        # 设置价格提醒
        return await set_price_alert(action.parameters, context.user_id)
    
    elif action.type == "create_strategy":
        # 创建交易策略
        return await create_trading_strategy(action.parameters, context.user_id)
    
    else:
        raise ValueError(f"不支持的操作类型: {action.type}")

# 模拟的业务函数（实际应该调用真实的服务）
async def get_user_portfolio(user_id: str) -> Dict:
    """获取用户投资组合"""
    return {
        "total_value": 100000,
        "positions": [
            {"symbol": "000001", "quantity": 1000, "value": 15000},
            {"symbol": "000002", "quantity": 500, "value": 12000}
        ],
        "cash": 73000
    }

async def get_recent_trades(user_id: str) -> List[Dict]:
    """获取最近交易"""
    return [
        {"symbol": "000001", "action": "buy", "quantity": 100, "price": 15.0, "date": "2024-01-15"},
        {"symbol": "000002", "action": "sell", "quantity": 200, "price": 24.5, "date": "2024-01-14"}
    ]

async def get_risk_profile(user_id: str) -> Dict:
    """获取风险偏好"""
    return {
        "risk_tolerance": "moderate",
        "max_single_position": 0.1,
        "max_sector_exposure": 0.3,
        "stop_loss_threshold": 0.05
    }

async def get_user_preferences(user_id: str) -> Dict:
    """获取用户偏好"""
    return {
        "preferred_sectors": ["technology", "healthcare"],
        "investment_horizon": "long_term",
        "notification_preferences": ["email", "app"]
    }

async def place_buy_order(parameters: Dict, user_id: str) -> Dict:
    """下买入订单"""
    return {
        "order_id": "ORD123456",
        "status": "submitted",
        "symbol": parameters["symbol"],
        "quantity": parameters["quantity"]
    }

async def place_sell_order(parameters: Dict, user_id: str) -> Dict:
    """下卖出订单"""
    return {
        "order_id": "ORD123457",
        "status": "submitted",
        "symbol": parameters["symbol"],
        "quantity": parameters["quantity"]
    }

async def set_price_alert(parameters: Dict, user_id: str) -> Dict:
    """设置价格提醒"""
    return {
        "alert_id": "ALT123456",
        "status": "active",
        "condition": parameters["condition"]
    }

async def create_trading_strategy(parameters: Dict, user_id: str) -> Dict:
    """创建交易策略"""
    return {
        "strategy_id": "STR123456",
        "name": parameters["name"],
        "status": "created"
    }

async def generate_smart_suggestions(user_id: str) -> List[Dict]:
    """生成智能建议"""
    return [
        {
            "type": "stock_recommendation",
            "content": "基于技术分析，建议关注000001",
            "confidence": 0.85,
            "reasoning": "突破关键阻力位，成交量放大"
        },
        {
            "type": "portfolio_adjustment",
            "content": "建议减少金融板块配置",
            "confidence": 0.75,
            "reasoning": "行业估值偏高，政策风险增加"
        }
    ]

async def get_market_summary() -> Dict:
    """获取市场摘要"""
    try:
        # 从天枢星获取真实市场摘要
        from roles.tianshu_star.tianshu_star_service import TianshuStarService
        tianshu_service = TianshuStarService()
        market_data = await tianshu_service.get_market_overview()

        return {
            "trend": market_data.get("trend", "未知"),
            "sentiment": market_data.get("sentiment", "中性"),
            "key_drivers": market_data.get("key_drivers", [])
        }
    except Exception as e:
        logger.error(f"获取市场摘要失败: {e}")
        raise HTTPException(status_code=500, detail="无法获取市场摘要")

async def get_top_movers() -> List[Dict]:
    """获取涨跌幅榜"""
    try:
        # 从实时数据服务获取真实涨跌幅数据
        from services.realtime_data_pipeline import realtime_data_pipeline
        movers_data = await realtime_data_pipeline.get_top_movers()

        return movers_data if movers_data else []
    except Exception as e:
        logger.error(f"获取涨跌幅榜失败: {e}")
        raise HTTPException(status_code=500, detail="无法获取涨跌幅数据")

async def get_sector_rotation() -> Dict:
    """获取板块轮动"""
    try:
        # 从天枢星获取真实板块轮动数据
        from roles.tianshu_star.tianshu_star_service import TianshuStarService
        tianshu_service = TianshuStarService()
        sector_data = await tianshu_service.get_sector_rotation()

        return sector_data if sector_data else {"hot_sectors": [], "cold_sectors": []}
    except Exception as e:
        logger.error(f"获取板块轮动失败: {e}")
        raise HTTPException(status_code=500, detail="无法获取板块轮动数据")

async def get_sentiment_analysis() -> Dict:
    """获取情绪分析"""
    try:
        # 从瑶光星获取真实情绪分析数据
        from roles.yaoguang_star.services.enhanced_data_source_service import EnhancedDataSourceService
        data_service = EnhancedDataSourceService()
        sentiment_data = await data_service.get_market_sentiment()

        return {
            "overall_sentiment": sentiment_data.get("sentiment_score", 0),
            "fear_greed_index": sentiment_data.get("fear_greed_index", 50)
        }
    except Exception as e:
        logger.error(f"获取情绪分析失败: {e}")
        raise HTTPException(status_code=500, detail="无法获取情绪分析数据")

async def get_technical_signals() -> List[Dict]:
    """获取技术信号"""
    try:
        # 从天璇星获取真实技术信号
        from roles.tianxuan_star.tianxuan_star_service import TianxuanStarService
        tianxuan_service = TianxuanStarService()
        signals_data = await tianxuan_service.get_technical_signals()

        return signals_data if signals_data else []
    except Exception as e:
        logger.error(f"获取技术信号失败: {e}")
        raise HTTPException(status_code=500, detail="无法获取技术信号数据")
