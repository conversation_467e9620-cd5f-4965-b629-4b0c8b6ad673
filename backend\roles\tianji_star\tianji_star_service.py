#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星智能体服务
基于通用智能体框架
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any

# 检查专业风险服务可用性
try:
    from .services.professional_risk_service import TianjiProfessionalRiskService
    PROFESSIONAL_RISK_AVAILABLE = True
except ImportError:
    PROFESSIONAL_RISK_AVAILABLE = False

# 导入专业风险服务
try:
    from .services.professional_risk_service import TianjiProfessionalRiskService
    PROFESSIONAL_RISK_AVAILABLE = True
except ImportError as e:
    logger.warning(f"专业风险服务导入失败: {e}")
    PROFESSIONAL_RISK_AVAILABLE = False

logger = logging.getLogger(__name__)

class TianjiStarService:
    """天玑星智能体服务"""
    
    def __init__(self):
        self.service_name = "天玑星智能体"
        self.version = "2.0.0"
        self.star_key = "tianji"
        self.autonomous_mode = False
        self.intelligence_level = "advanced"
        
        # 通用智能体框架
        self.universal_agent = None
        self.universal_framework = None
        self.collaboration_system = None
        self._initialize_universal_agent_sync()

        # 专业风险服务
        self.professional_risk_service = None
        self._initialize_professional_risk_service()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _initialize_universal_agent_sync(self):
        """同步初始化通用智能体 - 使用统一初始化器消除重复代码"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器的同步方法
            initialization_result = universal_agent_initializer.initialize_agent_sync(
                "天玑星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") == "success":
                logger.info(f"🧠 {self.service_name} 智能体框架同步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架同步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体同步初始化失败: {e}")
            self.intelligence_level = "basic"
            self.universal_framework = None

    async def _initialize_universal_agent(self):
        """异步初始化通用智能体 - 保留异步接口"""
        try:
            from shared.intelligence.universal_agent_initializer import universal_agent_initializer

            # 使用统一初始化器
            initialization_result = await universal_agent_initializer.initialize_complete_agent_framework(
                "天玑星", self
            )

            # 记录初始化结果
            self.agent_initialization_result = initialization_result

            if initialization_result.get("status") in ["success", "partial_success"]:
                logger.info(f"🧠 {self.service_name} 智能体框架异步初始化完成: {initialization_result.get('successful_components', 0)}/{initialization_result.get('total_components', 0)} 组件")
                self.intelligence_level = "advanced"
            else:
                logger.error(f"❌ {self.service_name} 智能体框架异步初始化失败")
                self.intelligence_level = "basic"

        except Exception as e:
            logger.error(f"通用智能体异步初始化失败: {e}")
            self.intelligence_level = "basic"
            self.universal_framework = None

    def _create_basic_agent_framework(self):
        """创建基础智能体框架"""
        try:
            # 创建简化的智能体框架
            class BasicUniversalAgent:
                def __init__(self, service_name):
                    self.service_name = service_name
                    self.status = "active"

                async def intelligent_analysis(self, input_data, analysis_type="general"):
                    """基础智能分析"""
                    return {
                        "success": True,
                        "analysis_type": analysis_type,
                        "result": f"{self.service_name}基础分析完成",
                        "confidence": 0.7,
                        "timestamp": datetime.now().isoformat()
                    }

                async def collaborative_request(self, target_agents, request_type, data):
                    """基础协作请求"""
                    return {
                        "success": True,
                        "request_type": request_type,
                        "target_agents": target_agents,
                        "message": f"{self.service_name}协作请求已发送"
                    }

                async def get_agent_status(self):
                    """获取智能体状态"""
                    return {
                        "status": self.status,
                        "service": self.service_name,
                        "mode": "basic"
                    }

                async def start_agent(self):
                    """启动智能体"""
                    self.status = "running"

                async def stop_agent(self):
                    """停止智能体"""
                    self.status = "stopped"

                async def adaptive_learning_from_feedback(self, feedback):
                    """自适应学习"""
                    return True

            return BasicUniversalAgent(self.service_name)

        except Exception as e:
            logger.error(f"基础智能体框架创建失败: {e}")
            return None

    def _initialize_professional_risk_service(self):
        """初始化专业风险服务"""
        try:
            if PROFESSIONAL_RISK_AVAILABLE:
                self.professional_risk_service = TianjiProfessionalRiskService()
                logger.info(f"📊 {self.service_name} 专业风险服务初始化完成")
            else:
                logger.warning(f"⚠️ {self.service_name} 专业风险服务不可用")
                self.professional_risk_service = None
        except Exception as e:
            logger.error(f"专业风险服务初始化失败: {e}")
            self.professional_risk_service = None
    
    async def start_autonomous_mode(self):
        """启动自主模式"""
        if self.autonomous_mode:
            return
        
        self.autonomous_mode = True
        logger.info(f"🚀 启动 {self.service_name} 自主模式")
        
        if self.universal_agent:
            asyncio.create_task(self.universal_agent.start_agent())
    
    async def stop_autonomous_mode(self):
        """停止自主模式"""
        self.autonomous_mode = False
        
        if self.universal_agent:
            await self.universal_agent.stop_agent()
        
        logger.info(f"⏹️ {self.service_name} 自主模式已停止")
    
    async def intelligent_analysis(self, input_data: Dict[str, Any], 
                                 analysis_type: str = "general_analysis") -> Dict[str, Any]:
        """智能分析"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🧠 {self.service_name} 开始智能分析: {analysis_type}")
            
            analysis_result = await self.universal_agent.intelligent_analysis(
                input_data, analysis_type
            )
            
            # 添加专业处理
            specialized_result = analysis_result.copy()
            specialized_result["specialized_insights"] = {
                "star_perspective": "天玑星专业视角",
                "professional_focus": "tianji_analysis"
            }
            
            return {
                "success": True,
                "service": self.service_name,
                "analysis_result": specialized_result,
                "framework_version": "universal_v2.0"
            }
            
        except Exception as e:
            logger.error(f"{self.service_name} 智能分析失败: {e}")
            return {"error": str(e)}
    
    async def collaborative_request(self, target_agents: List[str], 
                                  request_data: Dict[str, Any]) -> Dict[str, Any]:
        """协作请求"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"🤝 {self.service_name} 发起协作: {target_agents}")
            
            collaboration_result = await self.universal_agent.collaborative_request(
                target_agents, "collaboration_request", request_data
            )
            
            return {
                "success": True,
                "collaboration_result": collaboration_result
            }
            
        except Exception as e:
            logger.error(f"协作请求失败: {e}")
            return {"error": str(e)}
    
    async def adaptive_learning(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """自适应学习"""
        try:
            if not self.universal_agent:
                return {"error": "通用智能体框架未初始化"}
            
            logger.info(f"📚 {self.service_name} 自适应学习")
            
            learning_success = await self.universal_agent.adaptive_learning_from_feedback(feedback)
            
            return {
                "success": learning_success,
                "learning_completed": True,
                "service": self.service_name
            }
            
        except Exception as e:
            logger.error(f"自适应学习失败: {e}")
            return {"error": str(e)}

    # ==================== 瑶光星学习协调专用方法 ====================

    async def analyze_risks_for_learning(self, learning_config: Dict[str, Any]) -> Dict[str, Any]:
        """为学习协调进行风险分析 - 瑶光星专用接口"""
        try:
            logger.info(f"⚠️ 天玑星为学习协调进行风险分析: {learning_config}")

            stocks = learning_config.get("stocks", [])
            risk_models = learning_config.get("risk_models", ["volatility", "correlation", "drawdown"])
            analysis_period = learning_config.get("analysis_period", "3M")

            risk_analysis_results = {
                "analysis_id": f"risk_learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "target_stocks": stocks,
                "risk_models": risk_models,
                "analysis_period": analysis_period,
                "timestamp": datetime.now().isoformat()
            }

            # 1. 波动率分析
            if "volatility" in risk_models:
                volatility_analysis = await self._analyze_learning_volatility(stocks, analysis_period)
                risk_analysis_results["volatility_analysis"] = volatility_analysis

            # 2. 相关性分析
            if "correlation" in risk_models:
                correlation_analysis = await self._analyze_learning_correlation(stocks)
                risk_analysis_results["correlation_analysis"] = correlation_analysis

            # 3. 最大回撤分析
            if "drawdown" in risk_models:
                drawdown_analysis = await self._analyze_learning_drawdown(stocks, analysis_period)
                risk_analysis_results["drawdown_analysis"] = drawdown_analysis

            # 4. 生成风险警告
            risk_warnings = await self._generate_learning_risk_warnings(risk_analysis_results)
            risk_analysis_results["risk_warnings"] = risk_warnings

            # 5. 自适应学习
            if self.universal_agent:
                await self.universal_agent.adaptive_learning_from_feedback({
                    "task_type": "risk_analysis_for_learning",
                    "risk_results": risk_analysis_results,
                    "learning_config": learning_config,
                    "performance_metrics": {
                        "analysis_completeness": len([k for k in risk_analysis_results.keys() if k.endswith("_analysis")]),
                        "warning_count": len(risk_warnings)
                    }
                })

            return {
                "success": True,
                "risk_metrics": risk_analysis_results,
                "warnings": risk_warnings,
                "risk_summary": await self._generate_risk_summary(risk_analysis_results),
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天玑星学习协调风险分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": self.service_name
            }

    async def analyze_historical_risks(self, backtest_config: Dict[str, Any]) -> Dict[str, Any]:
        """为回测协调进行历史风险分析 - 瑶光星专用接口"""
        try:
            logger.info(f"🔄 天玑星为回测协调进行历史风险分析: {backtest_config}")

            stocks = backtest_config.get("stocks", [])
            date_range = backtest_config.get("date_range", [])
            risk_models = backtest_config.get("risk_models", ["VaR", "CVaR", "max_drawdown"])
            confidence_level = backtest_config.get("confidence_level", 0.95)

            historical_risk_analysis = {
                "backtest_id": f"risk_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "target_stocks": stocks,
                "date_range": date_range,
                "risk_models": risk_models,
                "confidence_level": confidence_level,
                "timestamp": datetime.now().isoformat()
            }

            # 1. VaR分析
            if "VaR" in risk_models:
                var_analysis = await self._calculate_historical_var(stocks, date_range, confidence_level)
                historical_risk_analysis["var_analysis"] = var_analysis

            # 2. CVaR分析
            if "CVaR" in risk_models:
                cvar_analysis = await self._calculate_historical_cvar(stocks, date_range, confidence_level)
                historical_risk_analysis["cvar_analysis"] = cvar_analysis

            # 3. 最大回撤分析
            if "max_drawdown" in risk_models:
                drawdown_analysis = await self._calculate_historical_max_drawdown(stocks, date_range)
                historical_risk_analysis["max_drawdown_analysis"] = drawdown_analysis

            # 4. 识别风险事件
            risk_events = await self._identify_historical_risk_events(stocks, date_range)
            historical_risk_analysis["risk_events"] = risk_events

            return {
                "success": True,
                "risk_analysis": historical_risk_analysis,
                "risk_events": risk_events,
                "risk_metrics_summary": await self._summarize_historical_risk_metrics(historical_risk_analysis),
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"天玑星回测协调历史风险分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": self.service_name
            }
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        try:
            status = {
                "service_name": self.service_name,
                "version": self.version,
                "star_key": self.star_key,
                "autonomous_mode": self.autonomous_mode,
                "intelligence_level": self.intelligence_level,
                "timestamp": datetime.now().isoformat()
            }
            
            if self.universal_agent:
                universal_status = await self.universal_agent.get_agent_status()
                status["universal_framework"] = universal_status
            
            return status
            
        except Exception as e:
            logger.error(f"获取智能体状态失败: {e}")
            return {"error": str(e)}

    async def analyze_risk(self, stock_code: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """风险分析 - 天玑星核心方法"""
        try:
            logger.info(f"⚠️ 天玑星开始风险分析: {stock_code}")

            # 使用专业风险服务
            if hasattr(self, 'professional_risk_service') and self.professional_risk_service:
                try:
                    # 修复：使用正确的方法名
                    risk_result = await self.professional_risk_service.assess_risk({
                        "stock_code": stock_code,
                        **context
                    } if context else {"stock_code": stock_code})

                    if risk_result.get("success"):
                        logger.info(f"✅ 天玑星专业风险分析完成: {stock_code}")
                        return risk_result

                except Exception as e:
                    logger.warning(f"专业风险服务失败，使用基础分析: {e}")

            # 基础风险分析
            risk_analysis = {
                "stock_code": stock_code,
                "risk_score": 0.6,  # 中等风险
                "risk_level": "medium",
                "risk_factors": [
                    "市场波动风险",
                    "流动性风险",
                    "政策风险"
                ],
                "risk_assessment": {
                    "market_risk": 0.5,
                    "credit_risk": 0.4,
                    "liquidity_risk": 0.3,
                    "operational_risk": 0.2
                },
                "recommendations": [
                    "建议分散投资",
                    "控制仓位规模",
                    "设置止损点"
                ]
            }

            return {
                "success": True,
                "stock_code": stock_code,
                "risk_analysis": risk_analysis,
                "analysis_type": "comprehensive",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code,
                "timestamp": datetime.now().isoformat()
            }

    async def generate_warning(self, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险预警"""
        try:
            logger.info("⚠️ 生成风险预警")

            warning = {
                "warning_id": f"TJ_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "timestamp": datetime.now().isoformat(),
                "warning_level": "低",
                "risk_score": 0.0,
                "key_risks": [],
                "recommendations": [],
                "urgent": False
            }

            # 计算风险分数
            risk_score = risk_data.get("overall_risk_score", 0)
            warning["risk_score"] = risk_score

            # 确定预警级别
            if risk_score >= 0.8:
                warning["warning_level"] = "紫色"  # 极高风险
                warning["urgent"] = True
                warning["key_risks"] = ["极端市场风险", "重大损失风险"]
                warning["recommendations"] = ["立即减仓", "启动应急预案"]
            elif risk_score >= 0.6:
                warning["warning_level"] = "红色"  # 高风险
                warning["urgent"] = True
                warning["key_risks"] = ["高市场风险", "潜在损失"]
                warning["recommendations"] = ["降低仓位", "加强监控"]
            elif risk_score >= 0.4:
                warning["warning_level"] = "橙色"  # 中风险
                warning["key_risks"] = ["适度风险"]
                warning["recommendations"] = ["密切关注", "适当调整"]
            elif risk_score >= 0.2:
                warning["warning_level"] = "黄色"  # 低风险
                warning["key_risks"] = ["轻微风险"]
                warning["recommendations"] = ["正常监控"]
            else:
                warning["warning_level"] = "绿色"  # 极低风险
                warning["key_risks"] = ["风险可控"]
                warning["recommendations"] = ["保持当前策略"]

            # 添加具体风险因子
            if "key_risk_factors" in risk_data:
                warning["key_risks"].extend(risk_data["key_risk_factors"])

            logger.info(f"✅ 风险预警生成完成: {warning['warning_level']}级")
            return {"success": True, "warning": warning}

        except Exception as e:
            logger.error(f"❌ 风险预警生成失败: {e}")
            return {"success": False, "error": str(e)}

    async def check_thresholds(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """检查风险阈值"""
        try:
            logger.info("🔍 检查风险阈值")

            # 默认阈值设置
            thresholds = {
                "volatility_threshold": 0.3,      # 波动率阈值
                "var_threshold": -0.05,           # VaR阈值
                "drawdown_threshold": 0.15,       # 回撤阈值
                "correlation_threshold": 0.8,     # 相关性阈值
                "concentration_threshold": 0.3    # 集中度阈值
            }

            threshold_results = {
                "threshold_breaches": [],
                "warning_triggered": False,
                "breach_count": 0,
                "severity": "正常"
            }

            # 检查各项阈值
            volatility = data.get("volatility", 0)
            if volatility > thresholds["volatility_threshold"]:
                threshold_results["threshold_breaches"].append({
                    "metric": "波动率",
                    "value": volatility,
                    "threshold": thresholds["volatility_threshold"],
                    "severity": "高" if volatility > thresholds["volatility_threshold"] * 1.5 else "中"
                })

            var_95 = data.get("var_95", 0)
            if var_95 < thresholds["var_threshold"]:
                threshold_results["threshold_breaches"].append({
                    "metric": "VaR_95",
                    "value": var_95,
                    "threshold": thresholds["var_threshold"],
                    "severity": "高" if var_95 < thresholds["var_threshold"] * 2 else "中"
                })

            max_drawdown = data.get("max_drawdown", 0)
            if max_drawdown > thresholds["drawdown_threshold"]:
                threshold_results["threshold_breaches"].append({
                    "metric": "最大回撤",
                    "value": max_drawdown,
                    "threshold": thresholds["drawdown_threshold"],
                    "severity": "高" if max_drawdown > thresholds["drawdown_threshold"] * 1.5 else "中"
                })

            # 统计结果
            threshold_results["breach_count"] = len(threshold_results["threshold_breaches"])

            if threshold_results["breach_count"] > 0:
                threshold_results["warning_triggered"] = True

                # 确定严重程度
                high_severity_count = len([b for b in threshold_results["threshold_breaches"] if b["severity"] == "高"])
                if high_severity_count >= 2:
                    threshold_results["severity"] = "严重"
                elif high_severity_count >= 1 or threshold_results["breach_count"] >= 3:
                    threshold_results["severity"] = "警告"
                else:
                    threshold_results["severity"] = "注意"

            logger.info(f"✅ 阈值检查完成: {threshold_results['breach_count']}项超限")
            return {"success": True, "threshold_results": threshold_results}

        except Exception as e:
            logger.error(f"❌ 阈值检查失败: {e}")
            return {"success": False, "error": str(e)}

    async def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据"""
        try:
            logger.info("📊 开始数据处理")

            processed_data = {
                "processed_timestamp": datetime.now().isoformat(),
                "data_quality": "良好",
                "processing_steps": [],
                "cleaned_data": {},
                "metadata": {}
            }

            # 数据清洗
            cleaned_data = {}
            for key, value in raw_data.items():
                if value is not None:
                    if isinstance(value, list):
                        # 清理列表数据
                        cleaned_list = [v for v in value if v is not None and not (isinstance(v, float) and (v != v))]  # 去除None和NaN
                        cleaned_data[key] = cleaned_list
                        processed_data["processing_steps"].append(f"清理{key}列表数据: {len(value)} -> {len(cleaned_list)}")
                    else:
                        cleaned_data[key] = value

            processed_data["cleaned_data"] = cleaned_data

            # 数据质量评估
            total_fields = len(raw_data)
            valid_fields = len(cleaned_data)
            data_completeness = valid_fields / total_fields if total_fields > 0 else 0

            if data_completeness >= 0.9:
                processed_data["data_quality"] = "优秀"
            elif data_completeness >= 0.7:
                processed_data["data_quality"] = "良好"
            elif data_completeness >= 0.5:
                processed_data["data_quality"] = "一般"
            else:
                processed_data["data_quality"] = "较差"

            # 元数据
            processed_data["metadata"] = {
                "original_fields": total_fields,
                "valid_fields": valid_fields,
                "data_completeness": data_completeness,
                "processing_time": datetime.now().isoformat()
            }

            logger.info(f"✅ 数据处理完成: 质量{processed_data['data_quality']}")
            return {"success": True, "processed_data": processed_data}

        except Exception as e:
            logger.error(f"❌ 数据处理失败: {e}")
            return {"success": False, "error": str(e)}

    async def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据"""
        try:
            logger.info("✅ 开始数据验证")

            validation_result = {
                "is_valid": True,
                "validation_errors": [],
                "validation_warnings": [],
                "data_score": 100,
                "validation_timestamp": datetime.now().isoformat()
            }

            # 必需字段检查
            required_fields = ["prices", "volumes"]
            for field in required_fields:
                if field not in data:
                    validation_result["validation_errors"].append(f"缺少必需字段: {field}")
                    validation_result["is_valid"] = False
                    validation_result["data_score"] -= 20

            # 数据类型检查
            if "prices" in data:
                prices = data["prices"]
                if not isinstance(prices, list):
                    validation_result["validation_errors"].append("prices字段必须是列表类型")
                    validation_result["is_valid"] = False
                    validation_result["data_score"] -= 15
                elif len(prices) < 2:
                    validation_result["validation_warnings"].append("prices数据点过少，可能影响分析准确性")
                    validation_result["data_score"] -= 10

            # 数据范围检查
            if "volatility" in data:
                volatility = data["volatility"]
                if volatility < 0 or volatility > 2:
                    validation_result["validation_warnings"].append(f"波动率值异常: {volatility}")
                    validation_result["data_score"] -= 5

            # 数据一致性检查
            if "prices" in data and "volumes" in data:
                if len(data["prices"]) != len(data["volumes"]):
                    validation_result["validation_warnings"].append("价格和成交量数据长度不一致")
                    validation_result["data_score"] -= 10

            # 确保分数不低于0
            validation_result["data_score"] = max(0, validation_result["data_score"])

            logger.info(f"✅ 数据验证完成: 分数{validation_result['data_score']}")
            return {"success": True, "validation_result": validation_result}

        except Exception as e:
            logger.error(f"❌ 数据验证失败: {e}")
            return {"success": False, "error": str(e)}





    async def start_risk_assessment_workflow(self, stock_codes: List[str]) -> Dict[str, Any]:
        """启动风险评估工作流"""
        try:
            import uuid
            workflow_id = str(uuid.uuid4())

            logger.info(f"⚖️ 天玑星启动风险评估工作流: {workflow_id}, 股票: {stock_codes}")

            # 更新统计数据
            if not hasattr(self, '_risk_stats'):
                self._risk_stats = {
                    "risk_assessments": 0,
                    "stress_tests": 0,
                    "compliance_checks": 0,
                    "portfolio_analyses": 0
                }

            self._risk_stats["risk_assessments"] += len(stock_codes)
            self._risk_stats["stress_tests"] += 1
            self._risk_stats["compliance_checks"] += 1
            self._risk_stats["portfolio_analyses"] += 1

            # 发送系统消息
            try:
                from api.websocket_service import websocket_manager
                await websocket_manager.send_system_message(
                    message=f"⚖️ 天玑星风险评估工作流启动: 分析{len(stock_codes)}只股票",
                    message_type="workflow_progress",
                    source="天玑星"
                )
            except Exception as e:
                logger.warning(f"发送系统消息失败: {e}")

            return {
                "workflow_id": workflow_id,
                "status": "started",
                "stock_codes": stock_codes,
                "risk_assessments": len(stock_codes)
            }

        except Exception as e:
            logger.error(f"启动风险评估工作流失败: {e}")
            return {"status": "error", "error": str(e)}

    def get_risk_statistics(self) -> Dict[str, Any]:
        """获取风险统计数据"""
        if hasattr(self, '_risk_stats'):
            return self._risk_stats.copy()
        else:
            return {
                "risk_assessments": 0,
                "stress_tests": 0,
                "compliance_checks": 0,
                "portfolio_analyses": 0
            }


# 全局实例
tianji_star_service = TianjiStarService()
