<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/天枢.png" alt="天枢星" />
          </div>
          <div class="role-details">
            <h1>天枢星 - 双核心情报统计中心</h1>
            <p>消息面 + 基本面双核心情报收集与智能分析</p>
          </div>
        </div>
        <div class="header-actions">
          <el-select v-model="selectedPeriod" @change="loadStatistics" placeholder="选择周期" size="default">
            <el-option label="日统计" value="daily" />
            <el-option label="周统计" value="weekly" />
            <el-option label="月统计" value="monthly" />
          </el-select>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 双核心概览 -->
    <div class="dual-core-overview">
      <el-card>
        <template #header>
          <h3>天枢星双核心概览</h3>
        </template>
        <div class="dual-core-grid">
          <div class="core-section message-core">
            <div class="core-header">
              <div class="core-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="core-info">
                <h4>消息面核心</h4>
                <p>新闻、社交、政策情报收集</p>
              </div>
            </div>
            <div class="core-metrics">
              <div class="metric-item">
                <span class="metric-label">新闻情报</span>
                <span class="metric-value">{{ messageCore.news }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">社交情报</span>
                <span class="metric-value">{{ messageCore.social }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">政策情报</span>
                <span class="metric-value">{{ messageCore.policy }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">消息面质量</span>
                <span class="metric-value">{{ messageCore.quality }}%</span>
              </div>
            </div>
          </div>

          <div class="core-section fundamental-core">
            <div class="core-header">
              <div class="core-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="core-info">
                <h4>基本面核心</h4>
                <p>市场、国际基本面数据收集</p>
              </div>
            </div>
            <div class="core-metrics">
              <div class="metric-item">
                <span class="metric-label">市场数据</span>
                <span class="metric-value">{{ fundamentalCore.market }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">国际情报</span>
                <span class="metric-value">{{ fundamentalCore.international }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">财务数据</span>
                <span class="metric-value">{{ fundamentalCore.financial }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">基本面质量</span>
                <span class="metric-value">{{ fundamentalCore.quality }}%</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.totalIntelligence }}</div>
          <div class="metric-label">情报收集总数</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.intelligenceTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (coreMetrics.qualityScore * 100).toFixed(1) }}%</div>
          <div class="metric-label">情报质量评分</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.qualityTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.avgCollectionTime }}s</div>
          <div class="metric-label">平均收集时间</div>
          <div class="metric-change negative">
            <el-icon><ArrowDown /></el-icon>
            -{{ coreMetrics.timeTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card info">
        <div class="metric-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.activeSources }}</div>
          <div class="metric-label">活跃情报源</div>
          <div class="metric-change neutral">
            <el-icon><Minus /></el-icon>
            {{ coreMetrics.sourcesTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 五大收集器状态 -->
    <div class="collectors-status">
      <el-card>
        <template #header>
          <h3>五大情报收集器状态</h3>
        </template>
        <div class="collectors-grid">
          <div 
            v-for="collector in collectorsStatus" 
            :key="collector.name"
            class="collector-card"
            :class="collector.status"
          >
            <div class="collector-header">
              <div class="collector-icon">
                <el-icon><component :is="collector.icon" /></el-icon>
              </div>
              <div class="collector-info">
                <h4>{{ collector.name }}</h4>
                <span class="collector-type">{{ collector.type }}</span>
              </div>
              <div class="collector-status-badge" :class="collector.status">
                {{ getStatusText(collector.status) }}
              </div>
            </div>
            <div class="collector-metrics">
              <div class="metric-item">
                <span class="metric-label">收集数量</span>
                <span class="metric-value">{{ collector.collected }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成功率</span>
                <span class="metric-value">{{ (collector.successRate * 100).toFixed(1) }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">响应时间</span>
                <span class="metric-value">{{ collector.responseTime }}s</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 情报收集趋势图 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>情报收集趋势</h3>
              <div class="chart-controls">
                <el-button-group>
                  <el-button 
                    v-for="period in trendPeriods" 
                    :key="period.value"
                    :type="selectedTrendPeriod === period.value ? 'primary' : 'default'"
                    @click="changeTrendPeriod(period.value)"
                    size="small"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <canvas ref="trendChart" width="800" height="300"></canvas>
          </div>
        </el-card>
      </div>

      <!-- 情报源分布饼图 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <h3>情报源分布</h3>
          </template>
          <div class="chart-content">
            <div class="source-distribution">
              <div class="distribution-chart">
                <canvas ref="distributionChart" width="300" height="300"></canvas>
              </div>
              <div class="distribution-legend">
                <div class="legend-item" v-for="item in sourceDistribution" :key="item.source">
                  <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                  <div class="legend-info">
                    <div class="legend-label">{{ item.label }}</div>
                    <div class="legend-value">{{ item.count }} ({{ item.percentage }}%)</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 详细统计表格 -->
    <div class="statistics-table">
      <el-card>
        <template #header>
          <div class="table-header">
            <h3>详细统计数据</h3>
            <el-button @click="exportStatistics">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </template>
        
        <el-table :data="statisticsData" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="newsCount" label="新闻情报" width="100" />
          <el-table-column prop="marketCount" label="市场情报" width="100" />
          <el-table-column prop="policyCount" label="政策情报" width="100" />
          <el-table-column prop="socialCount" label="社交情报" width="100" />
          <el-table-column prop="internationalCount" label="国际情报" width="100" />
          <el-table-column prop="qualityScore" label="质量评分" width="120">
            <template #default="scope">
              <el-tag :type="getQualityType(scope.row.qualityScore)">
                {{ (scope.row.qualityScore * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalCount" label="总计" width="100">
            <template #default="scope">
              <strong>{{ scope.row.totalCount }}</strong>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  Refresh, CircleCheck, Clock, TrendCharts, Minus, DataAnalysis,
  ArrowUp, ArrowDown, Download, Document, Monitor, ChatDotRound, Connection
} from '@element-plus/icons-vue'
import { TianshuStarAPI } from '@/api/roles/tianshu-star'

// 响应式数据
const selectedPeriod = ref('daily')
const selectedTrendPeriod = ref('7d')

// 双核心数据
const messageCore = reactive({
  news: 0,
  social: 0,
  policy: 0,
  quality: 0
})

const fundamentalCore = reactive({
  market: 0,
  international: 0,
  financial: 0,
  quality: 0
})

// 核心指标
const coreMetrics = reactive({
  totalIntelligence: 0,
  intelligenceTrend: 0,
  qualityScore: 0,
  qualityTrend: 0,
  avgCollectionTime: 0,
  timeTrend: 0,
  activeSources: 0,
  sourcesTrend: 0
})

// 五大收集器状态
const collectorsStatus = ref([
  {
    name: '新闻情报收集器',
    type: 'NewsIntelligenceCollector',
    icon: 'Document',
    status: 'active',
    collected: 0,
    successRate: 0,
    responseTime: 0
  },
  {
    name: '市场情报收集器',
    type: 'MarketIntelligenceCollector',
    icon: 'Monitor',
    status: 'active',
    collected: 0,
    successRate: 0,
    responseTime: 0
  },
  {
    name: '政策情报收集器',
    type: 'PolicyIntelligenceCollector',
    icon: 'Document',
    status: 'active',
    collected: 0,
    successRate: 0,
    responseTime: 0
  },
  {
    name: '社交媒体收集器',
    type: 'SocialMediaIntelligenceCollector',
    icon: 'ChatDotRound',
    status: 'active',
    collected: 0,
    successRate: 0,
    responseTime: 0
  },
  {
    name: '国际情报收集器',
    type: 'InternationalIntelligenceCollector',
    icon: 'Connection',
    status: 'active',
    collected: 0,
    successRate: 0,
    responseTime: 0
  }
])

// 情报源分布数据
const sourceDistribution = ref([
  { source: 'news', label: '新闻源', count: 0, percentage: 0, color: '#3b82f6' },
  { source: 'market', label: '市场源', count: 0, percentage: 0, color: '#10b981' },
  { source: 'policy', label: '政策源', count: 0, percentage: 0, color: '#f59e0b' },
  { source: 'social', label: '社交源', count: 0, percentage: 0, color: '#8b5cf6' },
  { source: 'international', label: '国际源', count: 0, percentage: 0, color: '#ef4444' }
])

// 统计数据
const statisticsData = ref([])

// 趋势周期选项
const trendPeriods = [
  { value: '1d', label: '1天' },
  { value: '7d', label: '7天' },
  { value: '30d', label: '30天' }
]

// 图表引用
const trendChart = ref(null)
const distributionChart = ref(null)

// 获取统计数据
const loadStatistics = async () => {
  try {
    console.log('开始加载天枢星统计数据...')
    const response = await TianshuStarAPI.getStatistics({
      period: selectedPeriod.value
    })

    console.log('天枢星统计数据响应:', response.data)

    if (response.data && response.data.success) {
      const data = response.data.data

      // 更新双核心数据
      const collectors = data.collectors_status || {}
      Object.assign(messageCore, {
        news: collectors.NewsIntelligenceCollector?.collected_count || 0,
        social: collectors.SocialMediaIntelligenceCollector?.collected_count || 0,
        policy: collectors.PolicyIntelligenceCollector?.collected_count || 0,
        quality: Math.round(((collectors.NewsIntelligenceCollector?.success_rate || 0) +
                           (collectors.SocialMediaIntelligenceCollector?.success_rate || 0) +
                           (collectors.PolicyIntelligenceCollector?.success_rate || 0)) / 3 * 100)
      })

      Object.assign(fundamentalCore, {
        market: collectors.MarketIntelligenceCollector?.collected_count || 0,
        international: collectors.InternationalIntelligenceCollector?.collected_count || 0,
        financial: collectors.FinancialIntelligenceCollector?.collected_count || 0, // 使用真实财务数据
        quality: Math.round(((collectors.MarketIntelligenceCollector?.success_rate || 0) +
                           (collectors.InternationalIntelligenceCollector?.success_rate || 0)) / 2 * 100)
      })

      // 更新核心指标
      coreMetrics.totalIntelligence = data.intelligence_statistics?.total_collected || 0
      coreMetrics.intelligenceTrend = data.intelligence_statistics?.collection_trend || 0
      coreMetrics.qualityScore = data.quality_assessment?.overall_quality || 0
      coreMetrics.qualityTrend = data.quality_assessment?.quality_trend || 0
      coreMetrics.avgCollectionTime = data.performance?.avg_collection_time || 0
      coreMetrics.timeTrend = data.performance?.time_trend || 0
      coreMetrics.activeSources = data.intelligence_statistics?.active_sources || 0
      coreMetrics.sourcesTrend = data.intelligence_statistics?.sources_trend || 0

      // 更新收集器状态
      await updateCollectorsStatus(data)

      // 更新统计表格数据
      await updateStatisticsData(data)

      // 更新图表
      nextTick(() => {
        drawCharts()
      })
    } else {
      console.warn('API返回失败状态:', response.data)
      setDefaultData()
    }
  } catch (error) {
    console.error('获取天枢星统计数据失败:', error)
    setDefaultData()
  }
}

// 更新收集器状态
const updateCollectorsStatus = async (data) => {
  try {
    const collectors = data.collectors_status || {}
    
    collectorsStatus.value.forEach((collector, index) => {
      const collectorData = collectors[collector.type] || {}
      collector.collected = collectorData.collected_count || 0
      collector.successRate = collectorData.success_rate || 0
      collector.responseTime = collectorData.avg_response_time || 0
      collector.status = collectorData.status || 'inactive'
    })
  } catch (error) {
    console.error('更新收集器状态失败:', error)
  }
}

// 更新统计表格数据
const updateStatisticsData = async (data) => {
  try {
    const history = data.daily_history || []
    
    statisticsData.value = history.map(item => ({
      date: item.date,
      newsCount: item.news_count || 0,
      marketCount: item.market_count || 0,
      policyCount: item.policy_count || 0,
      socialCount: item.social_count || 0,
      internationalCount: item.international_count || 0,
      qualityScore: item.quality_score || 0,
      totalCount: (item.news_count || 0) + (item.market_count || 0) + 
                  (item.policy_count || 0) + (item.social_count || 0) + 
                  (item.international_count || 0)
    }))

    // 更新情报源分布
    updateSourceDistribution(data)
  } catch (error) {
    console.error('更新统计数据失败:', error)
  }
}

// 更新情报源分布
const updateSourceDistribution = (data) => {
  try {
    const distribution = data.source_distribution || {}
    const total = Object.values(distribution).reduce((sum, count) => sum + (count || 0), 0)

    sourceDistribution.value.forEach(item => {
      const count = distribution[item.source] || 0
      item.count = count
      item.percentage = total > 0 ? Math.round((count / total) * 100) : 0
    })
  } catch (error) {
    console.error('更新情报源分布失败:', error)
  }
}

// 设置默认数据
const setDefaultData = () => {
  console.log('设置天枢星默认数据')
  Object.assign(coreMetrics, {
    totalIntelligence: 0,
    intelligenceTrend: 0,
    qualityScore: 0,
    qualityTrend: 0,
    avgCollectionTime: 0,
    timeTrend: 0,
    activeSources: 0,
    sourcesTrend: 0
  })

  collectorsStatus.value.forEach(collector => {
    collector.collected = 0
    collector.successRate = 0
    collector.responseTime = 0
    collector.status = 'inactive'
  })

  statisticsData.value = []
  sourceDistribution.value.forEach(item => {
    item.count = 0
    item.percentage = 0
  })
}

// 绘制图表
const drawCharts = () => {
  drawTrendChart()
  drawDistributionChart()
}

// 绘制趋势图
const drawTrendChart = () => {
  const canvas = trendChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  ctx.clearRect(0, 0, width, height)

  if (statisticsData.value.length === 0) {
    ctx.fillStyle = '#9ca3af'
    ctx.font = '14px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('暂无数据', width / 2, height / 2)
    return
  }

  const data = statisticsData.value.map(item => item.totalCount)
  const max = Math.max(...data)
  const min = Math.min(...data)
  const range = max - min || 1

  // 绘制网格线
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  for (let i = 0; i <= 5; i++) {
    const y = (height - 60) * i / 5 + 30
    ctx.beginPath()
    ctx.moveTo(50, y)
    ctx.lineTo(width - 30, y)
    ctx.stroke()
  }

  // 绘制趋势线
  ctx.strokeStyle = '#3b82f6'
  ctx.lineWidth = 3
  ctx.beginPath()

  data.forEach((value, index) => {
    const x = 50 + (width - 80) * index / (data.length - 1)
    const y = height - 30 - ((value - min) / range) * (height - 60)

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()

  // 绘制数据点
  ctx.fillStyle = '#3b82f6'
  data.forEach((value, index) => {
    const x = 50 + (width - 80) * index / (data.length - 1)
    const y = height - 30 - ((value - min) / range) * (height - 60)

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()
  })
}

// 绘制分布饼图
const drawDistributionChart = () => {
  const canvas = distributionChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const centerX = canvas.width / 2
  const centerY = canvas.height / 2
  const radius = 100

  ctx.clearRect(0, 0, canvas.width, canvas.height)

  const total = sourceDistribution.value.reduce((sum, item) => sum + item.count, 0)

  if (total === 0) {
    ctx.fillStyle = '#f3f4f6'
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = '#9ca3af'
    ctx.font = '14px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('暂无数据', centerX, centerY)
    return
  }

  let currentAngle = -Math.PI / 2

  sourceDistribution.value.forEach(item => {
    if (item.count > 0) {
      const sliceAngle = (item.count / total) * 2 * Math.PI

      ctx.fillStyle = item.color
      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
      ctx.closePath()
      ctx.fill()

      ctx.strokeStyle = '#ffffff'
      ctx.lineWidth = 2
      ctx.stroke()

      currentAngle += sliceAngle
    }
  })

  // 绘制中心圆
  ctx.fillStyle = 'white'
  ctx.beginPath()
  ctx.arc(centerX, centerY, 40, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#1a1a1a'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('总计', centerX, centerY - 5)
  ctx.fillText(total.toString(), centerX, centerY + 15)
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
}

// 切换趋势周期
const changeTrendPeriod = (period) => {
  selectedTrendPeriod.value = period
  drawTrendChart()
}

// 导出统计数据
const exportStatistics = () => {
  console.log('导出天枢星统计数据')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'active': '活跃',
    'inactive': '停用',
    'error': '错误',
    'warning': '警告'
  }
  return statusMap[status] || '未知'
}

// 获取质量类型
const getQualityType = (score) => {
  if (score >= 0.8) return 'success'
  if (score >= 0.6) return 'warning'
  return 'danger'
}

// 生命周期
onMounted(async () => {
  await loadStatistics()
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 双核心概览样式 */
.dual-core-overview {
  margin-bottom: 24px;
}

.dual-core-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.core-section {
  padding: 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.message-core {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  border-color: rgba(59, 130, 246, 0.2);
}

.fundamental-core {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
  border-color: rgba(16, 185, 129, 0.2);
}

.core-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.core-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 24px;
}

.message-core .core-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.fundamental-core .core-icon {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.core-info h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.core-info p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.core-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 收集器状态样式 */
.collectors-status {
  margin-bottom: 24px;
}

.collectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.collector-card {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.collector-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.collector-card.active {
  border-color: #10b981;
}

.collector-card.inactive {
  border-color: #9ca3af;
  opacity: 0.7;
}

.collector-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.collector-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.collector-info {
  flex: 1;
}

.collector-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.collector-type {
  font-size: 12px;
  color: #6b7280;
}

.collector-status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.collector-status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.collector-status-badge.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.collector-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.metric-item {
  text-align: center;
}

.metric-item .metric-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-item .metric-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

/* 情报源分布样式 */
.source-distribution {
  display: flex;
  align-items: center;
  gap: 24px;
}

.distribution-chart {
  flex-shrink: 0;
}

.distribution-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 12px;
}

.legend-info {
  flex: 1;
}

.legend-label {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.legend-value {
  font-size: 12px;
  color: #6b7280;
}
</style>
