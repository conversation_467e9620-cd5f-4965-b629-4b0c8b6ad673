#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星实时数据服务
提供真实的实时市场数据，不使用任何模拟数据
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

class RealTimeDataService:
    """天枢星实时数据服务"""
    
    def __init__(self):
        self.service_name = "天枢星实时数据服务"
        self.version = "1.0.0"
        
        # 数据库路径
        self.realtime_db_path = Path("data/stock_realtime.db")
        self.master_db_path = Path("data/stock_master.db")
        
        # 数据源配置
        self.data_sources = {
            "eastmoney": True,
            "local_database": True,
            "crawl4ai": True
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览数据"""
        try:
            logger.info("📊 获取实时市场概览数据")
            
            # 尝试从实时数据库获取数据
            market_data = await self._get_market_data_from_database()
            
            if market_data:
                logger.info("✅ 从数据库获取市场概览成功")
                return {
                    "success": True,
                    "data": market_data,
                    "source": "realtime_database",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 如果数据库没有数据，尝试从东方财富API获取
            try:
                from roles.kaiyang_star.services.realtime_stock_service import realtime_stock_service
                
                # 获取主要指数数据
                major_indices = ["000001.XSHG", "399001.XSHE", "399006.XSHE"]  # 上证、深证、创业板
                realtime_result = await realtime_stock_service.fetch_realtime_data(major_indices)
                
                if realtime_result.get("success"):
                    logger.info("✅ 从东方财富API获取市场概览成功")
                    return {
                        "success": True,
                        "data": {
                            "indices": realtime_result.get("data", []),
                            "market_status": "trading" if self._is_trading_hours() else "closed",
                            "update_time": datetime.now().isoformat()
                        },
                        "source": "eastmoney_api",
                        "timestamp": datetime.now().isoformat()
                    }
                
            except Exception as e:
                logger.warning(f"从东方财富API获取数据失败: {e}")
            
            # 最后的备用方案：从主数据库获取基础信息
            basic_data = await self._get_basic_market_info()
            return {
                "success": True,
                "data": basic_data,
                "source": "master_database",
                "timestamp": datetime.now().isoformat(),
                "note": "使用基础市场信息"
            }
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _get_market_data_from_database(self) -> Optional[Dict[str, Any]]:
        """从实时数据库获取市场数据"""
        try:
            if not self.realtime_db_path.exists():
                logger.warning("实时数据库不存在")
                return None
            
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            # 获取最新的市场数据
            cursor.execute("""
                SELECT stock_code, current_price, change_percent, volume, update_time
                FROM realtime_data 
                WHERE stock_code IN ('000001.XSHG', '399001.XSHE', '399006.XSHE')
                ORDER BY update_time DESC 
                LIMIT 10
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if rows:
                market_data = []
                for row in rows:
                    market_data.append({
                        "stock_code": row[0],
                        "current_price": row[1],
                        "change_percent": row[2],
                        "volume": row[3],
                        "update_time": row[4]
                    })
                
                return {
                    "indices": market_data,
                    "market_status": "trading" if self._is_trading_hours() else "closed",
                    "data_count": len(market_data)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"从数据库获取市场数据失败: {e}")
            return None
    
    async def _get_basic_market_info(self) -> Dict[str, Any]:
        """获取基础市场信息"""
        try:
            if not self.master_db_path.exists():
                return {
                    "market_status": "unknown",
                    "message": "无可用数据源"
                }
            
            conn = sqlite3.connect(self.master_db_path)
            cursor = conn.cursor()
            
            # 获取股票总数
            cursor.execute("SELECT COUNT(*) FROM stock_basic")
            total_stocks = cursor.fetchone()[0]
            
            # 获取行业分布
            cursor.execute("""
                SELECT industry, COUNT(*) as count 
                FROM stock_basic 
                WHERE industry IS NOT NULL 
                GROUP BY industry 
                ORDER BY count DESC 
                LIMIT 5
            """)
            
            industries = cursor.fetchall()
            conn.close()
            
            return {
                "total_stocks": total_stocks,
                "top_industries": [{"industry": row[0], "count": row[1]} for row in industries],
                "market_status": "trading" if self._is_trading_hours() else "closed",
                "data_source": "master_database"
            }
            
        except Exception as e:
            logger.error(f"获取基础市场信息失败: {e}")
            return {
                "market_status": "unknown",
                "error": str(e)
            }
    
    def _is_trading_hours(self) -> bool:
        """判断是否在交易时间"""
        now = datetime.now()
        weekday = now.weekday()
        
        # 周末不交易
        if weekday >= 5:
            return False
        
        # 交易时间：9:30-11:30, 13:00-15:00
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)
    
    async def get_stock_realtime_data(self, stock_codes: List[str]) -> Dict[str, Any]:
        """获取股票实时数据"""
        try:
            logger.info(f"📊 获取 {len(stock_codes)} 只股票的实时数据")
            
            # 尝试使用开阳星的实时数据服务
            try:
                from roles.kaiyang_star.services.realtime_stock_service import realtime_stock_service
                result = await realtime_stock_service.fetch_realtime_data(stock_codes)
                
                if result.get("success"):
                    logger.info(f"✅ 获取实时数据成功: {len(result.get('data', []))} 条")
                    return result
                
            except Exception as e:
                logger.warning(f"调用开阳星实时数据服务失败: {e}")
            
            # 备用方案：从数据库获取
            return await self._get_stocks_from_database(stock_codes)
            
        except Exception as e:
            logger.error(f"获取股票实时数据失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_stocks_from_database(self, stock_codes: List[str]) -> Dict[str, Any]:
        """从数据库获取股票数据"""
        try:
            if not self.realtime_db_path.exists():
                return {
                    "success": False,
                    "error": "实时数据库不存在"
                }
            
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            # 构建查询
            placeholders = ",".join(["?" for _ in stock_codes])
            cursor.execute(f"""
                SELECT stock_code, current_price, change_percent, volume, 
                       high_price, low_price, open_price, update_time
                FROM realtime_data 
                WHERE stock_code IN ({placeholders})
                ORDER BY update_time DESC
            """, stock_codes)
            
            rows = cursor.fetchall()
            conn.close()
            
            if rows:
                stock_data = []
                for row in rows:
                    stock_data.append({
                        "stock_code": row[0],
                        "current_price": row[1],
                        "change_percent": row[2],
                        "volume": row[3],
                        "high_price": row[4],
                        "low_price": row[5],
                        "open_price": row[6],
                        "update_time": row[7]
                    })
                
                return {
                    "success": True,
                    "data": stock_data,
                    "source": "realtime_database"
                }
            else:
                return {
                    "success": False,
                    "error": "数据库中没有找到相关股票数据"
                }
                
        except Exception as e:
            logger.error(f"从数据库获取股票数据失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局实例
real_time_data_service = RealTimeDataService()
