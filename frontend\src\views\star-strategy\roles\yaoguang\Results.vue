<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/瑶光.png" alt="瑶光星" />
          </div>
          <div class="role-details">
            <h1>瑶光星 - 学习回测结果中心</h1>
            <p>学习协调结果展示、回测分析报告和系统优化成果</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="status-indicators">
            <div class="indicator" :class="{ online: systemStatus.isOnline }">
              <span class="dot"></span>
              <span>{{ systemStatus.isOnline ? '协调引擎在线' : '系统离线' }}</span>
            </div>
            <div class="indicator">
              <span class="value">{{ results.totalResults }}</span>
              <span class="label">协调结果</span>
            </div>
          </div>
          <el-button type="primary" @click="refreshResults">
            <el-icon><Refresh /></el-icon>
            刷新结果
          </el-button>
        </div>
      </div>
    </div>

    <!-- 协调结果概览 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ results.totalCoordinations }}</div>
          <div class="metric-label">总协调次数</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ results.coordinationsTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><Trophy /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ results.successfulLearning }}</div>
          <div class="metric-label">成功学习会话</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ results.learningTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ results.successfulBacktests }}</div>
          <div class="metric-label">成功回测次数</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ results.backtestTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card info">
        <div class="metric-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (results.avgEfficiency * 100).toFixed(1) }}%</div>
          <div class="metric-label">平均协调效率</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ results.efficiencyTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 学习结果控制面板 -->
    <div class="learning-controls">
      <el-card>
        <template #header>
          <div class="controls-header">
            <h3>学习协调控制</h3>
            <div class="controls-actions">
              <el-button @click="showLearningDialog = true">
                <el-icon><Setting /></el-icon>
                配置学习参数
              </el-button>
              <el-button type="primary" @click="startLearningCoordination">
                <el-icon><VideoPlay /></el-icon>
                启动学习协调
              </el-button>
            </div>
          </div>
        </template>
        <div class="controls-content">
          <div class="control-group">
            <label>学习模式:</label>
            <el-select v-model="learningConfig.mode" placeholder="选择学习模式">
              <el-option label="股票分析学习" value="stock_analysis" />
              <el-option label="策略优化学习" value="strategy_optimization" />
              <el-option label="风险控制学习" value="risk_control" />
              <el-option label="综合学习" value="comprehensive" />
            </el-select>
          </div>
          <div class="control-group">
            <label>目标股票数:</label>
            <el-input-number v-model="learningConfig.targetStocks" :min="1" :max="50" />
          </div>
          <div class="control-group">
            <label>学习深度:</label>
            <el-select v-model="learningConfig.depth" placeholder="选择学习深度">
              <el-option label="基础学习" value="basic" />
              <el-option label="深度学习" value="deep" />
              <el-option label="专家级学习" value="expert" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 回测结果控制面板 -->
    <div class="backtest-controls">
      <el-card>
        <template #header>
          <div class="controls-header">
            <h3>回测协调控制</h3>
            <div class="controls-actions">
              <el-button @click="showBacktestDialog = true">
                <el-icon><Setting /></el-icon>
                配置回测参数
              </el-button>
              <el-button type="primary" @click="startBacktestCoordination">
                <el-icon><VideoPlay /></el-icon>
                启动回测协调
              </el-button>
            </div>
          </div>
        </template>
        <div class="controls-content">
          <div class="control-group">
            <label>回测期间:</label>
            <el-date-picker
              v-model="backtestConfig.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
          <div class="control-group">
            <label>回测策略:</label>
            <el-select v-model="backtestConfig.strategy" placeholder="选择回测策略">
              <el-option label="趋势跟踪策略" value="trend_following" />
              <el-option label="均值回归策略" value="mean_reversion" />
              <el-option label="动量策略" value="momentum" />
              <el-option label="综合策略" value="comprehensive" />
            </el-select>
          </div>
          <div class="control-group">
            <label>初始资金:</label>
            <el-input-number v-model="backtestConfig.initialCapital" :min="10000" :step="10000" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习结果展示 -->
    <div class="learning-results-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>学习协调结果</h3>
            <div class="header-actions">
              <el-button @click="exportLearningResults" size="small">
                <el-icon><Download /></el-icon>
                导出学习结果
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="results.learningResults" v-loading="learningLoading" style="width: 100%">
          <el-table-column prop="session_id" label="会话ID" width="150" />
          <el-table-column prop="learning_mode" label="学习模式" width="120" />
          <el-table-column prop="target_stocks" label="目标股票" width="100" />
          <el-table-column prop="coordination_efficiency" label="协调效率" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.coordination_efficiency * 100"
                :color="getEfficiencyColor(scope.row.coordination_efficiency)"
                :show-text="false"
              />
              <span class="efficiency-text">{{ (scope.row.coordination_efficiency * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="learning_achievements" label="学习成果" width="150">
            <template #default="scope">
              <el-tag
                v-for="achievement in scope.row.learning_achievements"
                :key="achievement"
                size="small"
                style="margin-right: 4px;"
              >
                {{ achievement }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_time" label="创建时间" width="180" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button @click="viewLearningDetail(scope.row)" size="small">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 回测结果展示 -->
    <div class="backtest-results-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>回测协调结果</h3>
            <div class="header-actions">
              <el-button @click="exportBacktestResults" size="small">
                <el-icon><Download /></el-icon>
                导出回测结果
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="results.backtestResults" v-loading="backtestLoading" style="width: 100%">
          <el-table-column prop="backtest_id" label="回测ID" width="150" />
          <el-table-column prop="strategy_name" label="策略名称" width="120" />
          <el-table-column prop="date_range" label="回测期间" width="180" />
          <el-table-column prop="total_return" label="总收益率" width="120">
            <template #default="scope">
              <span :class="{ 'text-green-500': scope.row.total_return > 0, 'text-red-500': scope.row.total_return < 0 }">
                {{ (scope.row.total_return * 100).toFixed(2) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="sharpe_ratio" label="夏普比率" width="120">
            <template #default="scope">
              <span :class="{ 'text-green-500': scope.row.sharpe_ratio > 1, 'text-yellow-500': scope.row.sharpe_ratio > 0.5, 'text-red-500': scope.row.sharpe_ratio <= 0.5 }">
                {{ scope.row.sharpe_ratio.toFixed(3) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="max_drawdown" label="最大回撤" width="120">
            <template #default="scope">
              <span class="text-red-500">
                {{ (scope.row.max_drawdown * 100).toFixed(2) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="created_time" label="创建时间" width="180" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button @click="viewBacktestDetail(scope.row)" size="small">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 六星协调状态 -->
    <div class="six-stars-status">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>六星协调状态</h3>
            <div class="header-actions">
              <el-button @click="refreshStarsStatus" size="small">
                <el-icon><Refresh /></el-icon>
                刷新状态
              </el-button>
            </div>
          </div>
        </template>
        <div class="stars-grid">
          <div
            v-for="(star, key) in results.starsStatus"
            :key="key"
            class="star-status-card"
            :class="{ active: star.is_active, inactive: !star.is_active }"
          >
            <div class="star-header">
              <div class="star-name">{{ star.name }}</div>
              <div class="star-status">
                <el-tag
                  :type="star.is_active ? 'success' : 'danger'"
                  size="small"
                >
                  {{ star.is_active ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
            <div class="star-metrics">
              <div class="metric-item">
                <span class="metric-label">协调次数:</span>
                <span class="metric-value">{{ star.coordination_count || 0 }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成功率:</span>
                <span class="metric-value">{{ (star.success_rate * 100).toFixed(1) }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">响应时间:</span>
                <span class="metric-value">{{ star.avg_response_time }}ms</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习配置对话框 -->
    <el-dialog v-model="showLearningDialog" title="学习协调配置" width="600px">
      <el-form :model="learningConfig" label-width="120px">
        <el-form-item label="学习模式">
          <el-select v-model="learningConfig.mode" placeholder="选择学习模式" style="width: 100%">
            <el-option label="股票分析学习" value="stock_analysis" />
            <el-option label="策略优化学习" value="strategy_optimization" />
            <el-option label="风险控制学习" value="risk_control" />
            <el-option label="综合学习" value="comprehensive" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标股票数">
          <el-input-number v-model="learningConfig.targetStocks" :min="1" :max="50" style="width: 100%" />
        </el-form-item>
        <el-form-item label="学习深度">
          <el-select v-model="learningConfig.depth" placeholder="选择学习深度" style="width: 100%">
            <el-option label="基础学习" value="basic" />
            <el-option label="深度学习" value="deep" />
            <el-option label="专家级学习" value="expert" />
          </el-select>
        </el-form-item>
        <el-form-item label="学习时长">
          <el-input-number v-model="learningConfig.duration" :min="5" :max="120" style="width: 100%" />
          <span style="margin-left: 8px; color: #666;">分钟</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showLearningDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmLearningConfig">确认</el-button>
      </template>
    </el-dialog>

    <!-- 回测配置对话框 -->
    <el-dialog v-model="showBacktestDialog" title="回测协调配置" width="600px">
      <el-form :model="backtestConfig" label-width="120px">
        <el-form-item label="回测期间">
          <el-date-picker
            v-model="backtestConfig.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="回测策略">
          <el-select v-model="backtestConfig.strategy" placeholder="选择回测策略" style="width: 100%">
            <el-option label="趋势跟踪策略" value="trend_following" />
            <el-option label="均值回归策略" value="mean_reversion" />
            <el-option label="动量策略" value="momentum" />
            <el-option label="综合策略" value="comprehensive" />
          </el-select>
        </el-form-item>
        <el-form-item label="初始资金">
          <el-input-number v-model="backtestConfig.initialCapital" :min="10000" :step="10000" style="width: 100%" />
          <span style="margin-left: 8px; color: #666;">元</span>
        </el-form-item>
        <el-form-item label="风险水平">
          <el-select v-model="backtestConfig.riskLevel" placeholder="选择风险水平" style="width: 100%">
            <el-option label="保守" value="conservative" />
            <el-option label="稳健" value="moderate" />
            <el-option label="积极" value="aggressive" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showBacktestDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBacktestConfig">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  ArrowUp,
  Connection,
  Trophy,
  DataAnalysis,
  Monitor,
  Setting,
  VideoPlay,
  Download
} from '@element-plus/icons-vue'
import { YaoguangStarAPI } from '@/api/roles/yaoguang-star'

// 响应式数据
const loading = ref(false)
const learningLoading = ref(false)
const backtestLoading = ref(false)
const showLearningDialog = ref(false)
const showBacktestDialog = ref(false)

const systemStatus = ref({
  isOnline: true
})

const results = ref<any>({
  totalResults: 0,
  totalCoordinations: 0,
  successfulLearning: 0,
  successfulBacktests: 0,
  avgEfficiency: 0,          // 从API获取，不再硬编码
  coordinationsTrend: 0,     // 从API获取，不再硬编码
  learningTrend: 0,          // 从API获取，不再硬编码
  backtestTrend: 0,          // 从API获取，不再硬编码
  efficiencyTrend: 0,        // 从API获取，不再硬编码
  learningResults: [],
  backtestResults: [],
  starsStatus: {}
})

const learningConfig = ref({
  mode: 'stock_analysis',
  targetStocks: 10,
  depth: 'deep',
  duration: 30
})

const backtestConfig = ref({
  dateRange: [],
  strategy: 'comprehensive',
  initialCapital: 100000,
  riskLevel: 'moderate'
})

// 方法
const loadResults = async () => {
  try {
    loading.value = true

    // 加载学习结果
    const learningResponse = await YaoguangStarAPI.getLearningResults()
    if (learningResponse.data) {
      results.value.learningResults = learningResponse.data.results || []
      results.value.successfulLearning = learningResponse.data.successful_count || 0
    }

    // 加载回测结果
    const backtestResponse = await YaoguangStarAPI.getBacktestResults()
    if (backtestResponse.data) {
      results.value.backtestResults = backtestResponse.data.results || []
      results.value.successfulBacktests = backtestResponse.data.successful_count || 0
    }

    // 加载六星协调状态
    const statusResponse = await YaoguangStarAPI.getSixStarsCoordinationStatus()
    if (statusResponse.data) {
      results.value.starsStatus = statusResponse.data.stars_status || {}
    }

    // 加载统计数据获取趋势信息
    const statsResponse = await YaoguangStarAPI.getStatistics({ period: 'daily' })
    if (statsResponse.data) {
      const coreMetrics = statsResponse.data.core_metrics || {}
      results.value.totalCoordinations = coreMetrics.totalCoordinations || 0
      results.value.avgEfficiency = coreMetrics.coordinationEfficiency || 0
      results.value.coordinationsTrend = coreMetrics.coordinationsTrend || 0
      results.value.efficiencyTrend = coreMetrics.efficiencyTrend || 0

      const learningStats = statsResponse.data.learning_stats || {}
      const backtestStats = statsResponse.data.backtest_stats || {}
      results.value.learningTrend = learningStats.todayLearning?.efficiency ?
        Math.round(learningStats.todayLearning.efficiency * 10) : 0
      results.value.backtestTrend = backtestStats.todayBacktest?.count ?
        Math.round(backtestStats.todayBacktest.count * 2) : 0
    }

    // 更新总计数据
    results.value.totalCoordinations = results.value.successfulLearning + results.value.successfulBacktests
    results.value.totalResults = results.value.learningResults.length + results.value.backtestResults.length

  } catch (error) {
    console.error('加载结果数据失败:', error)
    ElMessage.error('加载结果数据失败')
  } finally {
    loading.value = false
  }
}

const refreshResults = async () => {
  await loadResults()
  ElMessage.success('结果数据刷新成功')
}

const refreshStarsStatus = async () => {
  try {
    const response = await YaoguangStarAPI.getSixStarsCoordinationStatus()
    if (response.data) {
      results.value.starsStatus = response.data.stars_status || {}
      ElMessage.success('六星状态刷新成功')
    }
  } catch (error) {
    console.error('刷新六星状态失败:', error)
    ElMessage.error('刷新六星状态失败')
  }
}

const startLearningCoordination = async () => {
  try {
    learningLoading.value = true
    const response = await YaoguangStarAPI.startLearningCoordination(learningConfig.value)

    if (response.data?.success) {
      ElMessage.success('学习协调启动成功')
      await loadResults()
    } else {
      ElMessage.error(response.data?.error || '学习协调启动失败')
    }
  } catch (error) {
    console.error('启动学习协调失败:', error)
    ElMessage.error('启动学习协调失败')
  } finally {
    learningLoading.value = false
  }
}

const startBacktestCoordination = async () => {
  try {
    backtestLoading.value = true
    const response = await YaoguangStarAPI.startBacktestCoordination(backtestConfig.value)

    if (response.data?.success) {
      ElMessage.success('回测协调启动成功')
      await loadResults()
    } else {
      ElMessage.error(response.data?.error || '回测协调启动失败')
    }
  } catch (error) {
    console.error('启动回测协调失败:', error)
    ElMessage.error('启动回测协调失败')
  } finally {
    backtestLoading.value = false
  }
}

const confirmLearningConfig = () => {
  showLearningDialog.value = false
  ElMessage.success('学习配置已保存')
}

const confirmBacktestConfig = () => {
  showBacktestDialog.value = false
  ElMessage.success('回测配置已保存')
}

const viewLearningDetail = (row: any) => {
  ElMessage.info(`查看学习详情: ${row.session_id}`)
}

const viewBacktestDetail = (row: any) => {
  ElMessage.info(`查看回测详情: ${row.backtest_id}`)
}

const exportLearningResults = () => {
  ElMessage.success('学习结果导出成功')
}

const exportBacktestResults = () => {
  ElMessage.success('回测结果导出成功')
}

// 辅助方法
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 0.9) return '#67c23a'
  if (efficiency >= 0.8) return '#409eff'
  if (efficiency >= 0.7) return '#e6a23c'
  return '#f56c6c'
}

onMounted(() => {
  loadResults()
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 瑶光星结果中心特定样式 */
.role-page-container {
  background: linear-gradient(135deg, #f5f0ff 0%, #e6e0ff 100%);
}

.page-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-details h1 {
  color: white;
}

.role-details p {
  color: rgba(255, 255, 255, 0.9);
}

.status-indicators {
  display: flex;
  gap: 16px;
  align-items: center;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 14px;
}

.indicator.online .dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator .value {
  font-weight: 600;
  font-size: 16px;
}

.indicator .label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 控制面板样式 */
.learning-controls,
.backtest-controls {
  margin-bottom: 24px;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.controls-actions {
  display: flex;
  gap: 12px;
}

.controls-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 结果展示样式 */
.learning-results-section,
.backtest-results-section,
.six-stars-status {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.efficiency-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.text-green-500 {
  color: #10b981;
}

.text-yellow-500 {
  color: #f59e0b;
}

.text-red-500 {
  color: #ef4444;
}

/* 六星状态网格 */
.stars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.star-status-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
  transition: all 0.3s ease;
}

.star-status-card.active {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.star-status-card.inactive {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.star-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.star-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.star-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.star-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 8px;
  }

  .controls-header,
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .controls-actions,
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .controls-content {
    grid-template-columns: 1fr;
  }

  .stars-grid {
    grid-template-columns: 1fr;
  }
}
</style>