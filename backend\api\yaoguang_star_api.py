"""
瑶光星API接口 - 提供前端访问瑶光星功能的接口
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Body, Path, Depends
from pydantic import BaseModel

from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
from roles.yaoguang_star.core.data_persistence import yaoguang_persistence

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(prefix="/api/roles/yaoguang", tags=["瑶光星"])
yaoguang_star_router = router  # 为main.py提供正确的路由名称

# 数据模型
class LearningRequest(BaseModel):
    mode: str
    target_stocks: int
    depth: str
    duration: Optional[int] = 30

class BacktestRequest(BaseModel):
    date_range: List[str]
    strategy: str
    initial_capital: float
    risk_level: str

# API接口
@router.get("/system/status")
async def get_system_status():
    """获取瑶光星系统状态"""
    try:
        status = await unified_yaoguang_system.get_system_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/performance/dashboard")
async def get_performance_dashboard():
    """获取瑶光星绩效数据"""
    try:
        # 从数据库获取真实绩效数据
        performance_data = await unified_yaoguang_system.get_performance_metrics()
        
        # 获取协调健康状态
        coordination_health = await unified_yaoguang_system.get_stars_health_status()
        
        # 获取协调历史记录
        coordination_history = await unified_yaoguang_system.get_coordination_history()
        
        # 获取优化建议
        optimization_suggestions = await unified_yaoguang_system.get_optimization_suggestions()
        
        return {
            "success": True,
            "data": {
                "overview": performance_data.get("overview", {}),
                "core_metrics": performance_data.get("core_metrics", {}),
                "coordination_health": coordination_health,
                "learning_coordination": performance_data.get("learning_coordination", {}),
                "backtest_coordination": performance_data.get("backtest_coordination", {}),
                "coordination_history": coordination_history,
                "optimization_suggestions": optimization_suggestions,
                "coordination_metrics": performance_data.get("coordination_metrics", {})
            }
        }
    except Exception as e:
        logger.error(f"获取绩效数据失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/learning/results")
async def get_learning_results(limit: int = 10, offset: int = 0):
    """获取瑶光星学习协调结果"""
    try:
        # 从数据库获取真实学习结果
        results = await unified_yaoguang_system.get_learning_results(limit, offset)
        
        return {
            "success": True,
            "data": {
                "results": results.get("results", []),
                "total": results.get("total", 0),
                "successful_count": results.get("successful_count", 0)
            }
        }
    except Exception as e:
        logger.error(f"获取学习结果失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/backtest/results")
async def get_backtest_results(limit: int = 10, offset: int = 0):
    """获取瑶光星回测协调结果"""
    try:
        # 从数据库获取真实回测结果
        results = await unified_yaoguang_system.get_backtest_results(limit, offset)
        
        return {
            "success": True,
            "data": {
                "results": results.get("results", []),
                "total": results.get("total", 0),
                "successful_count": results.get("successful_count", 0)
            }
        }
    except Exception as e:
        logger.error(f"获取回测结果失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/statistics")
async def get_statistics(period: str = "daily"):
    """获取瑶光星统计数据"""
    try:
        # 从数据库获取真实统计数据
        stats = await unified_yaoguang_system.get_statistics(period)
        
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取统计数据失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/task/logs")
async def get_task_logs(start_date: Optional[str] = None, end_date: Optional[str] = None):
    """获取瑶光星任务日志"""
    try:
        # 从数据库获取真实任务日志
        logs = await unified_yaoguang_system.get_task_logs(start_date, end_date)
        
        return {
            "success": True,
            "data": logs
        }
    except Exception as e:
        logger.error(f"获取任务日志失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/learning/start")
async def start_learning_coordination(request: LearningRequest):
    """启动学习协调流程"""
    try:
        # 启动真实学习协调流程
        result = await unified_yaoguang_system.start_learning_coordination(
            mode=request.mode,
            target_stocks=request.target_stocks,
            depth=request.depth,
            duration=request.duration
        )
        
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"启动学习协调失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/backtest/start")
async def start_backtest_coordination(request: BacktestRequest):
    """启动回测协调流程"""
    try:
        # 启动真实回测协调流程
        result = await unified_yaoguang_system.start_backtest_coordination(
            date_range=request.date_range,
            strategy=request.strategy,
            initial_capital=request.initial_capital,
            risk_level=request.risk_level
        )
        
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"启动回测协调失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/coordination/status")
async def get_coordination_status():
    """获取六星协调状态"""
    try:
        # 获取真实六星协调状态
        status = await unified_yaoguang_system.get_stars_coordination_status()
        
        return {
            "success": True,
            "data": {
                "stars_status": status
            }
        }
    except Exception as e:
        logger.error(f"获取协调状态失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/coordination/metrics")
async def get_agent_coordination_metrics():
    """获取智能体协调指标"""
    try:
        # 获取真实智能体协调指标
        metrics = await unified_yaoguang_system.get_agent_coordination_metrics()
        
        return {
            "success": True,
            "data": {
                "agent_metrics": metrics
            }
        }
    except Exception as e:
        logger.error(f"获取协调指标失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/service/health")
async def get_service_health():
    """获取瑶光星服务健康状态"""
    try:
        # 获取真实服务健康状态
        health = await unified_yaoguang_system.get_service_health()
        
        return {
            "success": True,
            "data": health
        }
    except Exception as e:
        logger.error(f"获取服务健康状态失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/automation/status")
async def get_automation_status():
    """获取瑶光星自动化系统状态"""
    try:
        # 获取自动化引擎状态
        automation_status = {
            "automation_engine": {
                "status": "active" if hasattr(unified_yaoguang_system, 'automation_engine') and unified_yaoguang_system.automation_engine else "inactive",
                "components": {}
            },
            "scheduling": {
                "daily_cycle": "configured",
                "learning_automation": "available",
                "trading_automation": "available",
                "quantitative_research": "available"
            },
            "six_stars_integration": {},
            "system_health": "good",
            "automation_features": {
                "stock_selection": "开阳星自动选股",
                "market_analysis": "天枢星自动市场分析",
                "risk_management": "天玑星自动风险控制",
                "technical_analysis": "天璇星自动技术分析",
                "strategy_matching": "天权星自动策略匹配",
                "trade_execution": "玉衡星自动交易执行"
            }
        }

        # 检查六星集成状态
        if hasattr(unified_yaoguang_system, 'automation_engine') and unified_yaoguang_system.automation_engine:
            for star_name, service in unified_yaoguang_system.automation_engine.items():
                automation_status["automation_engine"]["components"][star_name] = {
                    "status": "active" if service else "inactive",
                    "service_type": type(service).__name__ if service else "None"
                }

        # 检查六星协调状态
        try:
            stars_status = await unified_yaoguang_system.get_stars_coordination_status()
            for star_name, status in stars_status.items():
                automation_status["six_stars_integration"][star_name] = {
                    "online": status.get("is_online", False),
                    "automation_ready": status.get("is_online", False),
                    "current_tasks": status.get("current_tasks", 0)
                }
        except Exception as e:
            logger.warning(f"获取六星状态失败: {e}")

        return {
            "success": True,
            "data": automation_status
        }
    except Exception as e:
        logger.error(f"获取瑶光星自动化状态失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/automation/learning/start")
async def start_automated_learning():
    """启动自动化学习循环"""
    try:
        result = await unified_yaoguang_system.start_automated_learning_cycle()

        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"启动自动化学习失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/automation/backtest/start")
async def start_automated_backtest():
    """启动自动化回测循环"""
    try:
        result = await unified_yaoguang_system.start_automated_backtest_cycle()

        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"启动自动化回测失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/performance/enhanced")
async def get_enhanced_system_performance():
    """获取增强的系统性能指标"""
    try:
        result = await unified_yaoguang_system.get_enhanced_system_performance()

        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"获取增强系统性能失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/automation/comprehensive/start")
async def start_comprehensive_automation():
    """启动综合自动化套件"""
    try:
        result = await unified_yaoguang_system.start_comprehensive_automation_suite()

        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"启动综合自动化套件失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/fallback/history")
async def get_fallback_history():
    """获取降级处理历史"""
    try:
        fallback_history = getattr(unified_yaoguang_system, 'fallback_history', [])

        return {
            "success": True,
            "data": {
                "fallback_history": fallback_history,
                "total_events": len(fallback_history),
                "recent_events": fallback_history[-10:] if fallback_history else []
            }
        }
    except Exception as e:
        logger.error(f"获取降级处理历史失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }
