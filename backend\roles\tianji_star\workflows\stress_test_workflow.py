#!/usr/bin/env python3
"""
压力测试工作流
天玑星-风险管理 - 投资组合压力测试工作流实现
"""


# 添加当前目录到Python路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from config.database_config import get_database_path
except ImportError:
    # 如果导入失败，使用默认路径
    def get_database_path():
        return "backend/data/stock_master.db"

import asyncio
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)

class StressTestScenario(Enum):
    """压力测试场景"""
    MARKET_CRASH = "market_crash"          # 市场崩盘
    INTEREST_RATE_SHOCK = "interest_rate_shock"  # 利率冲击
    SECTOR_ROTATION = "sector_rotation"    # 板块轮动
    LIQUIDITY_CRISIS = "liquidity_crisis"  # 流动性危机
    VOLATILITY_SPIKE = "volatility_spike"  # 波动率飙升
    CORRELATION_BREAKDOWN = "correlation_breakdown"  # 相关性失效
    CUSTOM_SCENARIO = "custom_scenario"    # 自定义场景

class StressTestWorkflow:
    """
    压力测试工作流
    """
    
    def __init__(self):
        self.workflow_id = f"stress_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.status = "pending"
        self.start_time = None
        self.end_time = None
        self.results = {}
        self.errors = []
        
        # 预定义压力测试场景
        self.stress_scenarios = self._define_stress_scenarios()
    
    def _define_stress_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """定义压力测试场景"""
        return {
            StressTestScenario.MARKET_CRASH.value: {
                "name": "市场崩盘场景",
                "description": "模拟2008年金融危机级别的市场下跌",
                "market_shock": -0.30,  # 市场下跌30%
                "volatility_multiplier": 3.0,  # 波动率放大3倍
                "correlation_increase": 0.8,  # 相关性增加到0.8
                "duration_days": 30
            },
            StressTestScenario.INTEREST_RATE_SHOCK.value: {
                "name": "利率冲击场景",
                "description": "央行突然大幅加息",
                "interest_rate_change": 0.02,  # 利率上升200bp
                "growth_stocks_impact": -0.15,  # 成长股下跌15%
                "value_stocks_impact": -0.05,  # 价值股下跌5%
                "duration_days": 7
            },
            StressTestScenario.SECTOR_ROTATION.value: {
                "name": "板块轮动场景",
                "description": "科技股大幅下跌，传统行业上涨",
                "tech_sector_shock": -0.25,
                "traditional_sector_boost": 0.10,
                "duration_days": 14
            },
            StressTestScenario.LIQUIDITY_CRISIS.value: {
                "name": "流动性危机场景",
                "description": "市场流动性枯竭",
                "liquidity_impact": -0.20,
                "bid_ask_spread_multiplier": 5.0,
                "small_cap_extra_impact": -0.10,
                "duration_days": 21
            },
            StressTestScenario.VOLATILITY_SPIKE.value: {
                "name": "波动率飙升场景",
                "description": "VIX指数飙升至极端水平",
                "volatility_multiplier": 4.0,
                "market_shock": -0.10,
                "duration_days": 10
            },
            StressTestScenario.CORRELATION_BREAKDOWN.value: {
                "name": "相关性失效场景",
                "description": "历史相关性关系失效",
                "correlation_randomization": True,
                "volatility_multiplier": 2.0,
                "duration_days": 30
            }
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行压力测试工作流"""
        try:
            logger.info(f"开始执行压力测试工作流: {self.workflow_id}")
            self.status = "running"
            self.start_time = datetime.now()
            
            portfolio = input_data.get("portfolio", [])
            scenarios = input_data.get("scenarios", [StressTestScenario.MARKET_CRASH.value])
            
            if not portfolio:
                raise ValueError("投资组合数据不能为空")
            
            # 验证投资组合
            validated_portfolio = await self._validate_portfolio(portfolio)
            self.results["portfolio"] = validated_portfolio
            
            # 收集基础数据
            market_data = await self._collect_market_data(validated_portfolio)
            self.results["market_data"] = market_data
            
            # 计算基准指标
            baseline_metrics = await self._calculate_baseline_metrics(validated_portfolio, market_data)
            self.results["baseline_metrics"] = baseline_metrics
            
            # 执行压力测试
            stress_test_results = {}
            for scenario in scenarios:
                if scenario in self.stress_scenarios:
                    logger.info(f"执行压力测试场景: {scenario}")
                    scenario_result = await self._run_stress_scenario(
                        scenario, validated_portfolio, market_data, baseline_metrics
                    )
                    stress_test_results[scenario] = scenario_result
                else:
                    logger.warning(f"未知的压力测试场景: {scenario}")
            
            self.results["stress_test_results"] = stress_test_results
            
            # 生成综合分析报告
            comprehensive_analysis = await self._generate_comprehensive_analysis(
                baseline_metrics, stress_test_results
            )
            self.results["comprehensive_analysis"] = comprehensive_analysis
            
            # 生成风险建议
            risk_recommendations = await self._generate_risk_recommendations(
                comprehensive_analysis, stress_test_results
            )
            self.results["risk_recommendations"] = risk_recommendations
            
            self.status = "completed"
            self.end_time = datetime.now()
            
            logger.info(f"压力测试工作流执行完成: {self.workflow_id}")
            return self.results
            
        except Exception as e:
            self.status = "failed"
            self.end_time = datetime.now()
            error_msg = f"压力测试工作流执行失败: {e}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            return {"error": error_msg}
    
    async def _validate_portfolio(self, portfolio: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证投资组合"""
        validated_portfolio = []
        total_value = 0
        
        for position in portfolio:
            stock_code = position.get("stock_code")
            market_value = position.get("market_value", 0)
            
            if not stock_code or market_value <= 0:
                continue
            
            validated_position = {
                "stock_code": stock_code,
                "market_value": float(market_value),
                "shares": position.get("shares", 0),
                "sector": position.get("sector", "未知"),
                "market_cap": position.get("market_cap", "中盘")
            }
            
            validated_portfolio.append(validated_position)
            total_value += market_value
        
        # 计算权重
        for position in validated_portfolio:
            position["weight"] = position["market_value"] / total_value if total_value > 0 else 0
        
        return validated_portfolio
    
    async def _collect_market_data(self, portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """收集市场数据"""
        market_data = {}
        
        for position in portfolio:
            stock_code = position["stock_code"]
            
            try:
                # 从数据库获取历史数据
                stock_data = await self._get_stock_historical_data(stock_code)
                market_data[stock_code] = stock_data
                
            except Exception as e:
                logger.warning(f"获取股票数据失败 {stock_code}: {e}")
                # 使用历史平均数据作为备用
                logger.warning(f"使用历史平均数据作为备用: {stock_code}")
                market_data[stock_code] = {
                    "returns": [0.0] * 252,  # 零收益率作为保守估计
                    "volatility": 0.20,  # 市场平均波动率
                    "beta": 1.0,  # 市场平均Beta
                    "sector": position.get("sector", "未知"),

                }
        
        return market_data
    
    async def _get_stock_historical_data(self, stock_code: str) -> Dict[str, Any]:
        """获取股票历史数据"""
        try:
            import sqlite3
            conn = sqlite3.connect(get_database_path("stock_historical"))
            cursor = conn.cursor()

            # 处理股票代码格式 (去掉交易所后缀)
            clean_stock_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

            # 获取最近一年的数据
            cursor.execute("""
                SELECT close_price, change_percent, volume
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 252
            """, (clean_stock_code,))
            
            data = cursor.fetchall()
            conn.close()
            
            if not data:
                raise ValueError(f"没有找到股票数据: {stock_code}")
            
            prices = [float(row[0]) for row in data if row[0]]
            returns = [float(row[1])/100 for row in data if row[1] is not None]
            volumes = [float(row[2]) for row in data if row[2]]
            
            # 计算统计指标
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0.25  # 年化波动率

            return {
                "prices": prices,
                "returns": returns,
                "volumes": volumes,
                "volatility": volatility,
                "beta": beta,
                "data_points": len(prices)
            }
            
        except Exception as e:
            logger.error(f"获取股票历史数据失败 {stock_code}: {e}")
            # 拒绝生成模拟数据，返回错误信息
            logger.error(f"无法获取股票{stock_code}的真实历史数据，拒绝生成模拟数据")
            return {
                "success": False,
                "error": "真实历史数据不可用，系统拒绝生成模拟数据",
                "stock_code": stock_code,
                "data_source": "none"
            }
    
    async def _calculate_baseline_metrics(self, portfolio: List[Dict[str, Any]], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算基准指标"""
        weights = np.array([pos["weight"] for pos in portfolio])
        returns = []
        volatilities = []
        betas = []
        
        for position in portfolio:
            stock_code = position["stock_code"]
            stock_market_data = market_data.get(stock_code, {})
            
            stock_returns = stock_market_data.get("returns", [0.0])
            avg_return = np.mean(stock_returns) if stock_returns else 0.0
            returns.append(avg_return)
            
            volatilities.append(stock_market_data.get("volatility", 0.25))
            betas.append(stock_market_data.get("beta", 1.0))
        
        returns_array = np.array(returns)
        volatilities_array = np.array(volatilities)
        betas_array = np.array(betas)
        
        # 投资组合指标
        portfolio_return = np.sum(weights * returns_array) * 252  # 年化收益率
        portfolio_volatility = np.sqrt(np.sum((weights * volatilities_array)**2))  # 年化波动率
        portfolio_beta = np.sum(weights * betas_array)
        
        # VaR计算 (95%置信度)
        portfolio_var_95 = portfolio_volatility * 1.645 / np.sqrt(252)  # 日VaR
        portfolio_var_99 = portfolio_volatility * 2.326 / np.sqrt(252)  # 日VaR
        
        # 最大回撤估算

        return {
            "portfolio_return": portfolio_return,
            "portfolio_volatility": portfolio_volatility,
            "portfolio_beta": portfolio_beta,
            "var_95": portfolio_var_95,
            "var_99": portfolio_var_99,
            "max_drawdown_estimate": max_drawdown_estimate,
            "sharpe_ratio": portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0,
            "weights": weights.tolist(),
            "individual_returns": returns,
            "individual_volatilities": volatilities,
            "individual_betas": betas
        }
    
    async def _run_stress_scenario(self, scenario_name: str, portfolio: List[Dict[str, Any]], 
                                 market_data: Dict[str, Any], baseline_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """运行压力测试场景"""
        scenario_config = self.stress_scenarios[scenario_name]
        
        # 应用压力测试冲击
        stressed_returns = []
        stressed_volatilities = []
        
        for i, position in enumerate(portfolio):
            stock_code = position["stock_code"]
            baseline_return = baseline_metrics["individual_returns"][i]
            baseline_volatility = baseline_metrics["individual_volatilities"][i]
            
            # 根据场景类型应用不同的冲击
            if scenario_name == StressTestScenario.MARKET_CRASH.value:
                shock = scenario_config["market_shock"]
                vol_multiplier = scenario_config["volatility_multiplier"]
                
                stressed_return = baseline_return + shock
                stressed_volatility = baseline_volatility * vol_multiplier
                
            elif scenario_name == StressTestScenario.INTEREST_RATE_SHOCK.value:
                # 根据股票类型应用不同冲击
                sector = position.get("sector", "未知")
                if "科技" in sector or "成长" in sector:
                    shock = scenario_config["growth_stocks_impact"]
                else:
                    shock = scenario_config["value_stocks_impact"]
                
                stressed_return = baseline_return + shock
                stressed_volatility = baseline_volatility * 1.5
                
            elif scenario_name == StressTestScenario.SECTOR_ROTATION.value:
                sector = position.get("sector", "未知")
                if "科技" in sector:
                    shock = scenario_config["tech_sector_shock"]
                else:
                    shock = scenario_config.get("traditional_sector_boost", 0)
                
                stressed_return = baseline_return + shock
                stressed_volatility = baseline_volatility * 1.2
                
            elif scenario_name == StressTestScenario.LIQUIDITY_CRISIS.value:
                market_cap = position.get("market_cap", "中盘")
                base_shock = scenario_config["liquidity_impact"]
                
                if market_cap == "小盘":
                    shock = base_shock + scenario_config["small_cap_extra_impact"]
                else:
                    shock = base_shock
                
                stressed_return = baseline_return + shock
                stressed_volatility = baseline_volatility * 2.0
                
            elif scenario_name == StressTestScenario.VOLATILITY_SPIKE.value:
                shock = scenario_config["market_shock"]
                vol_multiplier = scenario_config["volatility_multiplier"]
                
                stressed_return = baseline_return + shock
                stressed_volatility = baseline_volatility * vol_multiplier
                
            else:  # 默认场景
                stressed_return = baseline_return * 0.8  # 20%下跌
                stressed_volatility = baseline_volatility * 2.0
            
            stressed_returns.append(stressed_return)
            stressed_volatilities.append(stressed_volatility)
        
        # 计算压力测试后的投资组合指标
        weights = np.array(baseline_metrics["weights"])
        stressed_returns_array = np.array(stressed_returns)
        stressed_volatilities_array = np.array(stressed_volatilities)
        
        stressed_portfolio_return = np.sum(weights * stressed_returns_array) * 252
        stressed_portfolio_volatility = np.sqrt(np.sum((weights * stressed_volatilities_array)**2))
        
        # 计算损失
        return_loss = stressed_portfolio_return - baseline_metrics["portfolio_return"]
        volatility_increase = stressed_portfolio_volatility - baseline_metrics["portfolio_volatility"]
        
        # 计算压力测试VaR
        stressed_var_95 = stressed_portfolio_volatility * 1.645 / np.sqrt(252)
        stressed_var_99 = stressed_portfolio_volatility * 2.326 / np.sqrt(252)
        
        return {
            "scenario_name": scenario_config["name"],
            "scenario_description": scenario_config["description"],
            "stressed_portfolio_return": stressed_portfolio_return,
            "stressed_portfolio_volatility": stressed_portfolio_volatility,
            "return_loss": return_loss,
            "volatility_increase": volatility_increase,
            "stressed_var_95": stressed_var_95,
            "stressed_var_99": stressed_var_99,
            "stressed_sharpe_ratio": stressed_portfolio_return / stressed_portfolio_volatility if stressed_portfolio_volatility > 0 else 0,
            "individual_stressed_returns": stressed_returns,
            "individual_stressed_volatilities": stressed_volatilities
        }
    
    async def _generate_comprehensive_analysis(self, baseline_metrics: Dict[str, Any], 
                                             stress_test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合分析"""
        worst_case_scenario = None
        worst_case_loss = 0
        
        scenario_summary = []
        
        for scenario_name, result in stress_test_results.items():
            loss = result["return_loss"]
            if loss < worst_case_loss:
                worst_case_loss = loss
                worst_case_scenario = scenario_name
            
            scenario_summary.append({
                "scenario": scenario_name,
                "return_loss": loss,
                "volatility_increase": result["volatility_increase"],
                "stressed_var_99": result["stressed_var_99"],
                "severity": "高" if loss < -0.15 else "中" if loss < -0.05 else "低"
            })
        
        # 按损失排序
        scenario_summary.sort(key=lambda x: x["return_loss"])
        
        return {
            "worst_case_scenario": worst_case_scenario,
            "worst_case_loss": worst_case_loss,
            "scenario_summary": scenario_summary,
            "baseline_var_99": baseline_metrics["var_99"],
            "max_stressed_var_99": max([result["stressed_var_99"] for result in stress_test_results.values()]),
            "portfolio_resilience_score": max(0, 1 + worst_case_loss / 0.5)  # 0-1分数，损失越小分数越高
        }
    
    async def _generate_risk_recommendations(self, comprehensive_analysis: Dict[str, Any], 
                                           stress_test_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成风险建议"""
        recommendations = []
        
        worst_case_loss = comprehensive_analysis["worst_case_loss"]
        resilience_score = comprehensive_analysis["portfolio_resilience_score"]
        
        # 基于最坏情况损失的建议
        if worst_case_loss < -0.30:
            recommendations.append({
                "priority": "高",
                "type": "风险控制",
                "description": "投资组合在极端情况下面临严重损失",
                "action": "建议大幅降低风险敞口，增加防御性资产配置",
                "target": "将最大损失控制在20%以内"
            })
        elif worst_case_loss < -0.15:
            recommendations.append({
                "priority": "中",
                "type": "风险管理",
                "description": "投资组合风险敞口较高",
                "action": "适当降低高风险资产比例，增加对冲工具",
                "target": "提升投资组合韧性得分至0.7以上"
            })
        
        # 基于韧性得分的建议
        if resilience_score < 0.5:
            recommendations.append({
                "priority": "高",
                "type": "组合优化",
                "description": "投资组合韧性不足",
                "action": "重新平衡投资组合，增加低相关性资产",
                "target": "提升韧性得分至0.7以上"
            })
        
        # 基于具体场景的建议
        for scenario_name, result in stress_test_results.items():
            if result["return_loss"] < -0.20:
                if "market_crash" in scenario_name:
                    recommendations.append({
                        "priority": "中",
                        "type": "市场风险",
                        "description": "对市场崩盘场景敏感度过高",
                        "action": "增加避险资产，如债券、黄金等",
                        "target": "降低市场Beta至0.8以下"
                    })
                elif "interest_rate" in scenario_name:
                    recommendations.append({
                        "priority": "中",
                        "type": "利率风险",
                        "description": "对利率变化敏感度过高",
                        "action": "减少利率敏感型股票，增加价值股配置",
                        "target": "平衡成长股和价值股比例"
                    })
        
        if not recommendations:
            recommendations.append({
                "priority": "低",
                "type": "维持现状",
                "description": "投资组合风险控制良好",
                "action": "继续保持当前配置，定期监控",
                "target": "维持当前风险水平"
            })
        
        return recommendations
    
    def get_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "errors": self.errors,
            "available_scenarios": list(self.stress_scenarios.keys())
        }

# 工作流实例创建函数
def create_stress_test_workflow() -> StressTestWorkflow:
    """创建压力测试工作流实例"""
    return StressTestWorkflow()

__all__ = ['StressTestWorkflow', 'StressTestScenario', 'create_stress_test_workflow']
