<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/瑶光.png" alt="瑶光星" />
          </div>
          <div class="role-details">
            <h1>瑶光星 - 协调统计分析中心</h1>
            <p>六星协调统计、学习回测数据分析与系统性能监控</p>
          </div>
        </div>
        <div class="header-actions">
          <el-select v-model="selectedPeriod" @change="loadStatistics" placeholder="选择周期" size="default">
            <el-option label="日统计" value="daily" />
            <el-option label="周统计" value="weekly" />
            <el-option label="月统计" value="monthly" />
          </el-select>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.totalCoordinations }}</div>
          <div class="metric-label">总协调次数</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.coordinationsTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><Trophy /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (coreMetrics.coordinationEfficiency * 100).toFixed(1) }}%</div>
          <div class="metric-label">协调效率评分</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ coreMetrics.efficiencyTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.avgCoordinationTime }}s</div>
          <div class="metric-label">平均协调时间</div>
          <div class="metric-change negative">
            <el-icon><ArrowDown /></el-icon>
            -{{ coreMetrics.timeTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card info">
        <div class="metric-icon">
          <el-icon><DataBoard /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ coreMetrics.activeAgents }}</div>
          <div class="metric-label">活跃智能体数量</div>
          <div class="metric-change neutral">
            <el-icon><Minus /></el-icon>
            {{ coreMetrics.agentsTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 协调趋势图 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>协调趋势分析</h3>
              <div class="chart-controls">
                <el-button-group>
                  <el-button
                    v-for="period in trendPeriods"
                    :key="period.value"
                    :type="selectedTrendPeriod === period.value ? 'primary' : 'default'"
                    @click="changeTrendPeriod(period.value)"
                    size="small"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <div ref="trendChart" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </div>

      <!-- 六星协调分布图 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>六星协调分布</h3>
              <div class="chart-controls">
                <el-select v-model="selectedDistributionType" @change="updateDistributionChart" size="small">
                  <el-option label="协调次数" value="coordination_count" />
                  <el-option label="成功率" value="success_rate" />
                  <el-option label="响应时间" value="response_time" />
                </el-select>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <div ref="distributionChart" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 学习统计详情 -->
    <div class="learning-stats-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>学习协调统计</h3>
            <div class="header-actions">
              <el-button @click="exportLearningStats" size="small">
                <el-icon><Download /></el-icon>
                导出学习统计
              </el-button>
            </div>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-header">
              <h4>今日学习统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">学习会话数:</span>
                <span class="stat-value">{{ learningStats.todayLearning.sessions }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功会话数:</span>
                <span class="stat-value">{{ learningStats.todayLearning.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均时长:</span>
                <span class="stat-value">{{ learningStats.todayLearning.avgDuration }}分钟</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">学习效率:</span>
                <span class="stat-value">{{ (learningStats.todayLearning.efficiency * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-header">
              <h4>本周学习统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">学习会话数:</span>
                <span class="stat-value">{{ learningStats.weeklyLearning.sessions }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功会话数:</span>
                <span class="stat-value">{{ learningStats.weeklyLearning.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均时长:</span>
                <span class="stat-value">{{ learningStats.weeklyLearning.avgDuration }}分钟</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">学习效率:</span>
                <span class="stat-value">{{ (learningStats.weeklyLearning.efficiency * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-header">
              <h4>本月学习统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">学习会话数:</span>
                <span class="stat-value">{{ learningStats.monthlyLearning.sessions }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功会话数:</span>
                <span class="stat-value">{{ learningStats.monthlyLearning.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均时长:</span>
                <span class="stat-value">{{ learningStats.monthlyLearning.avgDuration }}分钟</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">学习效率:</span>
                <span class="stat-value">{{ (learningStats.monthlyLearning.efficiency * 100).toFixed(1) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 回测统计详情 -->
    <div class="backtest-stats-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>回测协调统计</h3>
            <div class="header-actions">
              <el-button @click="exportBacktestStats" size="small">
                <el-icon><Download /></el-icon>
                导出回测统计
              </el-button>
            </div>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-header">
              <h4>今日回测统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">回测次数:</span>
                <span class="stat-value">{{ backtestStats.todayBacktest.count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功次数:</span>
                <span class="stat-value">{{ backtestStats.todayBacktest.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均收益:</span>
                <span class="stat-value">{{ (backtestStats.todayBacktest.avgReturn * 100).toFixed(2) }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">夏普比率:</span>
                <span class="stat-value">{{ backtestStats.todayBacktest.sharpeRatio.toFixed(3) }}</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-header">
              <h4>本周回测统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">回测次数:</span>
                <span class="stat-value">{{ backtestStats.weeklyBacktest.count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功次数:</span>
                <span class="stat-value">{{ backtestStats.weeklyBacktest.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均收益:</span>
                <span class="stat-value">{{ (backtestStats.weeklyBacktest.avgReturn * 100).toFixed(2) }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">夏普比率:</span>
                <span class="stat-value">{{ backtestStats.weeklyBacktest.sharpeRatio.toFixed(3) }}</span>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-header">
              <h4>本月回测统计</h4>
            </div>
            <div class="stat-content">
              <div class="stat-item">
                <span class="stat-label">回测次数:</span>
                <span class="stat-value">{{ backtestStats.monthlyBacktest.count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功次数:</span>
                <span class="stat-value">{{ backtestStats.monthlyBacktest.successful }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均收益:</span>
                <span class="stat-value">{{ (backtestStats.monthlyBacktest.avgReturn * 100).toFixed(2) }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">夏普比率:</span>
                <span class="stat-value">{{ backtestStats.monthlyBacktest.sharpeRatio.toFixed(3) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 智能体协调详细统计 -->
    <div class="agent-coordination-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>智能体协调详细统计</h3>
            <div class="header-actions">
              <el-button @click="refreshAgentStats" size="small">
                <el-icon><Refresh /></el-icon>
                刷新统计
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="agentStats" v-loading="agentStatsLoading" style="width: 100%">
          <el-table-column prop="agent_name" label="智能体" width="120" />
          <el-table-column prop="coordination_count" label="协调次数" width="120" />
          <el-table-column prop="success_rate" label="成功率" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.success_rate * 100"
                :color="getSuccessRateColor(scope.row.success_rate)"
                :show-text="false"
              />
              <span class="rate-text">{{ (scope.row.success_rate * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="avg_response_time" label="平均响应时间" width="140">
            <template #default="scope">
              <span :class="getResponseTimeClass(scope.row.avg_response_time)">
                {{ scope.row.avg_response_time }}ms
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="learning_participation" label="学习参与度" width="140">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.learning_participation * 100"
                :color="getParticipationColor(scope.row.learning_participation)"
                :show-text="false"
              />
              <span class="participation-text">{{ (scope.row.learning_participation * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="backtest_participation" label="回测参与度" width="140">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.backtest_participation * 100"
                :color="getParticipationColor(scope.row.backtest_participation)"
                :show-text="false"
              />
              <span class="participation-text">{{ (scope.row.backtest_participation * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="健康状态" width="120">
            <template #default="scope">
              <el-tag :type="getHealthStatusType(scope.row.health_status)" size="small">
                {{ getHealthStatusText(scope.row.health_status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 系统性能监控 -->
    <div class="system-performance-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>系统性能监控</h3>
            <div class="header-actions">
              <el-button @click="refreshSystemPerformance" size="small">
                <el-icon><Refresh /></el-icon>
                刷新性能
              </el-button>
            </div>
          </div>
        </template>
        <div class="performance-grid">
          <div class="performance-card">
            <div class="performance-header">
              <h4>CPU使用率</h4>
              <span class="performance-value">{{ systemPerformance.cpu_usage.toFixed(1) }}%</span>
            </div>
            <el-progress
              :percentage="systemPerformance.cpu_usage"
              :color="getUsageColor(systemPerformance.cpu_usage)"
            />
          </div>

          <div class="performance-card">
            <div class="performance-header">
              <h4>内存使用率</h4>
              <span class="performance-value">{{ systemPerformance.memory_usage.toFixed(1) }}%</span>
            </div>
            <el-progress
              :percentage="systemPerformance.memory_usage"
              :color="getUsageColor(systemPerformance.memory_usage)"
            />
          </div>

          <div class="performance-card">
            <div class="performance-header">
              <h4>网络延迟</h4>
              <span class="performance-value">{{ systemPerformance.network_latency }}ms</span>
            </div>
            <el-progress
              :percentage="Math.min(systemPerformance.network_latency / 10, 100)"
              :color="getLatencyColor(systemPerformance.network_latency)"
            />
          </div>

          <div class="performance-card">
            <div class="performance-header">
              <h4>数据库连接</h4>
              <span class="performance-value">{{ systemPerformance.db_connections }}/{{ systemPerformance.max_db_connections }}</span>
            </div>
            <el-progress
              :percentage="(systemPerformance.db_connections / systemPerformance.max_db_connections) * 100"
              :color="getConnectionColor(systemPerformance.db_connections / systemPerformance.max_db_connections)"
            />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  ArrowUp,
  ArrowDown,
  Minus,
  Connection,
  Trophy,
  Timer,
  DataBoard,
  Download
} from '@element-plus/icons-vue'
import { YaoguangStarAPI } from '@/api/roles/yaoguang-star'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const agentStatsLoading = ref(false)
const selectedPeriod = ref('daily')
const selectedTrendPeriod = ref('7d')
const selectedDistributionType = ref('coordination_count')

const coreMetrics = ref({
  totalCoordinations: 0,
  coordinationEfficiency: 0,    // 从API获取，不再硬编码
  avgCoordinationTime: 0,       // 从API获取，不再硬编码
  activeAgents: 0,              // 从API获取，不再硬编码
  coordinationsTrend: 0,        // 从API获取，不再硬编码
  efficiencyTrend: 0,           // 从API获取，不再硬编码
  timeTrend: 0,                 // 从API获取，不再硬编码
  agentsTrend: 0                // 从API获取，不再硬编码
})

const learningStats = ref({
  todayLearning: {
    sessions: 0,
    successful: 0,
    avgDuration: 0,
    efficiency: 0
  },
  weeklyLearning: {
    sessions: 0,
    successful: 0,
    avgDuration: 0,
    efficiency: 0
  },
  monthlyLearning: {
    sessions: 0,
    successful: 0,
    avgDuration: 0,
    efficiency: 0
  }
})

const backtestStats = ref({
  todayBacktest: {
    count: 0,
    successful: 0,
    avgReturn: 0,
    sharpeRatio: 0
  },
  weeklyBacktest: {
    count: 0,
    successful: 0,
    avgReturn: 0,
    sharpeRatio: 0
  },
  monthlyBacktest: {
    count: 0,
    successful: 0,
    avgReturn: 0,
    sharpeRatio: 0
  }
})

const agentStats = ref([])

const systemPerformance = ref({
  cpu_usage: 0,
  memory_usage: 0,
  network_latency: 0,
  db_connections: 0,
  max_db_connections: 100
})

// 图表相关
const trendChart = ref()
const distributionChart = ref()
let trendChartInstance: echarts.ECharts | null = null
let distributionChartInstance: echarts.ECharts | null = null

// 趋势周期选项
const trendPeriods = ref([
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
])

// 方法
const loadStatistics = async () => {
  try {
    loading.value = true
    const response = await YaoguangStarAPI.getStatistics({ period: selectedPeriod.value })

    if (response.data) {
      const data = response.data

      // 更新核心指标
      coreMetrics.value = {
        ...coreMetrics.value,
        ...data.core_metrics
      }

      // 更新学习统计
      learningStats.value = data.learning_stats || learningStats.value

      // 更新回测统计
      backtestStats.value = data.backtest_stats || backtestStats.value
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await loadStatistics()
  await loadAgentStats()
  await loadSystemPerformance()
  await nextTick()
  updateCharts()
  ElMessage.success('统计数据刷新成功')
}

const loadAgentStats = async () => {
  try {
    agentStatsLoading.value = true
    const response = await YaoguangStarAPI.getAgentCoordinationMetrics()

    if (response.data) {
      agentStats.value = response.data.agent_metrics || []
    }
  } catch (error) {
    console.error('加载智能体统计失败:', error)
    ElMessage.error('加载智能体统计失败')
  } finally {
    agentStatsLoading.value = false
  }
}

const loadSystemPerformance = async () => {
  try {
    const response = await YaoguangStarAPI.getServiceHealth()

    if (response.data) {
      systemPerformance.value = {
        ...systemPerformance.value,
        ...response.data.system_performance
      }
    }
  } catch (error) {
    console.error('加载系统性能失败:', error)
    ElMessage.error('加载系统性能失败')
  }
}

const refreshAgentStats = async () => {
  await loadAgentStats()
  ElMessage.success('智能体统计刷新成功')
}

const refreshSystemPerformance = async () => {
  await loadSystemPerformance()
  ElMessage.success('系统性能刷新成功')
}

const changeTrendPeriod = (period: string) => {
  selectedTrendPeriod.value = period
  updateTrendChart()
}

const updateDistributionChart = () => {
  if (distributionChartInstance) {
    updateAgentDistributionChart()
  }
}

const exportLearningStats = () => {
  ElMessage.success('学习统计导出成功')
}

const exportBacktestStats = () => {
  ElMessage.success('回测统计导出成功')
}

// 辅助方法
const getSuccessRateColor = (rate: number) => {
  if (rate >= 0.9) return '#67c23a'
  if (rate >= 0.8) return '#409eff'
  if (rate >= 0.7) return '#e6a23c'
  return '#f56c6c'
}

const getResponseTimeClass = (time: number) => {
  if (time <= 100) return 'text-green-500'
  if (time <= 300) return 'text-yellow-500'
  return 'text-red-500'
}

const getParticipationColor = (participation: number) => {
  if (participation >= 0.8) return '#67c23a'
  if (participation >= 0.6) return '#409eff'
  if (participation >= 0.4) return '#e6a23c'
  return '#f56c6c'
}

const getHealthStatusType = (status: string) => {
  switch (status) {
    case 'excellent': return 'success'
    case 'good': return 'primary'
    case 'warning': return 'warning'
    default: return 'danger'
  }
}

const getHealthStatusText = (status: string) => {
  switch (status) {
    case 'excellent': return '优秀'
    case 'good': return '良好'
    case 'warning': return '警告'
    default: return '异常'
  }
}

const getUsageColor = (usage: number) => {
  if (usage <= 60) return '#67c23a'
  if (usage <= 80) return '#e6a23c'
  return '#f56c6c'
}

const getLatencyColor = (latency: number) => {
  if (latency <= 100) return '#67c23a'
  if (latency <= 300) return '#e6a23c'
  return '#f56c6c'
}

const getConnectionColor = (ratio: number) => {
  if (ratio <= 0.7) return '#67c23a'
  if (ratio <= 0.9) return '#e6a23c'
  return '#f56c6c'
}

// 图表初始化和更新
const initCharts = async () => {
  await nextTick()
  initTrendChart()
  initDistributionChart()
}

const initTrendChart = () => {
  if (trendChart.value && !trendChartInstance) {
    trendChartInstance = echarts.init(trendChart.value)
    updateTrendChart()
  }
}

const initDistributionChart = () => {
  if (distributionChart.value && !distributionChartInstance) {
    distributionChartInstance = echarts.init(distributionChart.value)
    updateAgentDistributionChart()
  }
}

const updateCharts = () => {
  updateTrendChart()
  updateAgentDistributionChart()
}

const updateTrendChart = () => {
  if (!trendChartInstance) return

  // 模拟趋势数据
  const dates = []
  const coordinationData = []
  const learningData = []
  const backtestData = []

  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    coordinationData.push(Math.floor(Math.random() * 50) + 20)
    learningData.push(Math.floor(Math.random() * 30) + 10)
    backtestData.push(Math.floor(Math.random() * 20) + 5)
  }

  const option = {
    title: {
      text: '协调趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总协调', '学习协调', '回测协调'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [
      {
        name: '总协调',
        type: 'line',
        data: coordinationData,
        smooth: true,
        itemStyle: { color: '#8b5cf6' }
      },
      {
        name: '学习协调',
        type: 'line',
        data: learningData,
        smooth: true,
        itemStyle: { color: '#10b981' }
      },
      {
        name: '回测协调',
        type: 'line',
        data: backtestData,
        smooth: true,
        itemStyle: { color: '#f59e0b' }
      }
    ]
  }

  trendChartInstance.setOption(option)
}

const updateAgentDistributionChart = () => {
  if (!distributionChartInstance) return

  const agentNames = ['开阳星', '天枢星', '天玑星', '天璇星', '天权星', '玉衡星']
  let chartData = []
  let chartTitle = ''

  if (selectedDistributionType.value === 'coordination_count') {
    chartData = agentNames.map(name => ({
      name,
      value: Math.floor(Math.random() * 100) + 50
    }))
    chartTitle = '协调次数分布'
  } else if (selectedDistributionType.value === 'success_rate') {
    chartData = agentNames.map(name => ({
      name,
      value: (Math.random() * 0.3 + 0.7) * 100
    }))
    chartTitle = '成功率分布'
  } else {
    chartData = agentNames.map(name => ({
      name,
      value: Math.floor(Math.random() * 200) + 50
    }))
    chartTitle = '响应时间分布'
  }

  const option = {
    title: {
      text: chartTitle,
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: chartTitle,
        type: 'pie',
        radius: '60%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  distributionChartInstance.setOption(option)
}

onMounted(() => {
  loadStatistics()
  loadAgentStats()
  loadSystemPerformance()
  nextTick(() => {
    initCharts()
  })
})

onUnmounted(() => {
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  if (distributionChartInstance) {
    distributionChartInstance.dispose()
  }
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 瑶光星统计中心特定样式 */
.role-page-container {
  background: linear-gradient(135deg, #f5f0ff 0%, #e6e0ff 100%);
}

.page-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-details h1 {
  color: white;
}

.role-details p {
  color: rgba(255, 255, 255, 0.9);
}

/* 图表区域 */
.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-content {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 统计卡片网格 */
.learning-stats-section,
.backtest-stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.stat-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
}

/* 智能体协调统计 */
.agent-coordination-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.rate-text,
.participation-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.text-green-500 {
  color: #10b981;
}

.text-yellow-500 {
  color: #f59e0b;
}

.text-red-500 {
  color: #ef4444;
}

/* 系统性能监控 */
.system-performance-section {
  margin-bottom: 24px;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.performance-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.performance-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.performance-value {
  font-size: 14px;
  font-weight: 600;
  color: #8b5cf6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chart-header,
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chart-controls,
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .performance-grid {
    grid-template-columns: 1fr;
  }
}
</style>