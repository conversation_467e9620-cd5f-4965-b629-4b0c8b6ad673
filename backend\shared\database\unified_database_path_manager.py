#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库路径管理器
解决数据库路径不一致问题，提供统一的路径解析和管理
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union
import sqlite3

logger = logging.getLogger(__name__)

class UnifiedDatabasePathManager:
    """统一数据库路径管理器"""
    
    def __init__(self):
        self.service_name = "统一数据库路径管理器"
        self.version = "1.0.0"
        
        # 确定项目根目录 - 数据在backend/data目录下（标准结构）
        self.backend_root = self._find_project_root()  # 现在直接返回backend目录
        self.project_root = self.backend_root.parent if self.backend_root.name == "backend" else self.backend_root
        # 标准数据目录结构：backend/data（永远不允许backend/backend/data这种错误结构）
        self.data_root = self.backend_root / "data"
        
        # 确保数据目录存在
        self.data_root.mkdir(parents=True, exist_ok=True)
        
        # 标准数据库路径映射 - 修复：正确指向对应数据库
        self.standard_database_paths = {
            # 主要数据库
            "stock_database": self.data_root / "stock_master.db",  # 修复：指向主数据库
            "realtime_database": self.data_root / "stock_realtime.db",
            "historical_database": self.data_root / "stock_historical.db",  # 修复：指向历史数据库
            "daily_database": self.data_root / "stock_historical.db",
            
            # 角色专用数据库
            "tianshu_database": self.data_root / "tianshu_intelligence.db",
            "tianxuan_database": self.data_root / "tianxuan_technical.db",
            "tianji_database": self.data_root / "tianji_risk.db",
            "tianquan_database": self.data_root / "tianquan_strategy.db",
            "yuheng_database": self.data_root / "yuheng_trading.db",
            "kaiyang_database": self.data_root / "kaiyang_screening.db",
            "yaoguang_database": self.data_root / "yaoguang_learning.db",
            
            # 系统数据库
            "legendary_memory": self.data_root / "legendary_memory.db",
            "news_knowledge": self.data_root / "news_knowledge_base.db",
            "risk_database": self.data_root / "risk_database.db",
            "trading_execution": self.data_root / "trading_execution.db",
            "alerts": self.data_root / "alerts.db",
            "decision_making": self.data_root / "decision_making.db",
            
            # 完整A股数据库
            "complete_a_stock": self.data_root / "complete_a_stock_library" / "complete_a_stock_master.db"
        }
        
        # 数据库别名映射（兼容旧代码）
        self.database_aliases = {
            "stock_info_database": "stock_database",
            "realtime_stock_data": "realtime_database",
            "historical_stock": "historical_database",
            "stock_data": "stock_database",
            "main_database": "stock_database"
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
        logger.info(f"📁 项目根目录: {self.project_root}")
        logger.info(f"📁 数据根目录: {self.data_root}")
    
    def _find_project_root(self) -> Path:
        """查找项目根目录 - 强制返回backend目录，避免在外部创建文件"""
        current_path = Path(__file__).resolve()

        # 强制使用backend目录作为根目录，避免在外部创建文件
        # 从当前文件路径向上查找backend目录
        backend_path = current_path
        while backend_path.name != "backend" and backend_path.parent != backend_path:
            backend_path = backend_path.parent

        if backend_path.name == "backend":
            # 返回backend目录本身，而不是其父目录
            logger.info(f"✅ 找到backend目录: {backend_path}")
            return backend_path
        else:
            # 最后的保险：强制使用当前文件所在的backend路径
            return Path(__file__).resolve().parent.parent.parent.parent.parent
    
    def get_database_path(self, db_name: str) -> Path:
        """
        获取数据库路径
        
        Args:
            db_name: 数据库名称
            
        Returns:
            数据库文件路径
        """
        # 处理别名
        actual_db_name = self.database_aliases.get(db_name, db_name)
        
        # 获取标准路径
        if actual_db_name in self.standard_database_paths:
            return self.standard_database_paths[actual_db_name]
        
        # 如果不在标准路径中，创建默认路径
        return self.data_root / f"{db_name}.db"
    
    def get_database_path_str(self, db_name: str) -> str:
        """获取数据库路径字符串"""
        return str(self.get_database_path(db_name))
    
    def check_database_exists(self, db_name: str) -> bool:
        """检查数据库文件是否存在"""
        db_path = self.get_database_path(db_name)
        return db_path.exists()
    
    def create_database_if_not_exists(self, db_name: str) -> bool:
        """如果数据库不存在则创建"""
        try:
            db_path = self.get_database_path(db_name)
            
            # 确保父目录存在
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not db_path.exists():
                # 创建空数据库
                with sqlite3.connect(str(db_path)) as conn:
                    conn.execute("SELECT 1")  # 简单查询以创建文件
                logger.info(f"✅ 创建数据库: {db_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建数据库失败 {db_name}: {e}")
            return False
    
    def get_all_database_paths(self) -> Dict[str, str]:
        """获取所有数据库路径"""
        return {name: str(path) for name, path in self.standard_database_paths.items()}
    
    def get_existing_databases(self) -> List[str]:
        """获取所有存在的数据库"""
        existing_dbs = []
        for db_name in self.standard_database_paths.keys():
            if self.check_database_exists(db_name):
                existing_dbs.append(db_name)
        return existing_dbs
    
    def get_missing_databases(self) -> List[str]:
        """获取缺失的数据库"""
        missing_dbs = []
        for db_name in self.standard_database_paths.keys():
            if not self.check_database_exists(db_name):
                missing_dbs.append(db_name)
        return missing_dbs
    
    def migrate_old_database_paths(self) -> Dict[str, bool]:
        """迁移旧的数据库路径"""
        migration_results = {}
        
        # 可能的旧路径
        old_path_patterns = [
            "backend/data/{db_name}.db",
            "backend/data/{db_name}.db",
            "backend/data/{db_name}.db",
            "backend/data/{db_name}.db",
            "../../../data/{db_name}.db"
        ]
        
        for db_name, standard_path in self.standard_database_paths.items():
            if standard_path.exists():
                migration_results[db_name] = True
                continue
            
            # 查找旧路径
            found_old_path = None
            for pattern in old_path_patterns:
                old_path = Path(pattern.format(db_name=db_name.replace("_database", "")))
                if old_path.exists():
                    found_old_path = old_path
                    break
            
            if found_old_path:
                try:
                    # 确保目标目录存在
                    standard_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 移动文件
                    found_old_path.rename(standard_path)
                    logger.info(f"✅ 迁移数据库: {found_old_path} -> {standard_path}")
                    migration_results[db_name] = True
                    
                except Exception as e:
                    logger.error(f"❌ 迁移数据库失败 {db_name}: {e}")
                    migration_results[db_name] = False
            else:
                migration_results[db_name] = False
        
        return migration_results
    
    def validate_database_integrity(self, db_name: str) -> Dict[str, any]:
        """验证数据库完整性"""
        result = {
            "exists": False,
            "readable": False,
            "tables_count": 0,
            "size_mb": 0,
            "error": None
        }
        
        try:
            db_path = self.get_database_path(db_name)
            
            if not db_path.exists():
                result["error"] = "数据库文件不存在"
                return result
            
            result["exists"] = True
            result["size_mb"] = round(db_path.stat().st_size / (1024 * 1024), 2)
            
            # 尝试连接和查询
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                result["tables_count"] = len(tables)
                result["readable"] = True
                
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def get_database_summary(self) -> Dict[str, any]:
        """获取数据库概要信息"""
        summary = {
            "total_databases": len(self.standard_database_paths),
            "existing_databases": 0,
            "missing_databases": 0,
            "total_size_mb": 0,
            "database_details": {}
        }
        
        for db_name in self.standard_database_paths.keys():
            validation = self.validate_database_integrity(db_name)
            summary["database_details"][db_name] = validation
            
            if validation["exists"]:
                summary["existing_databases"] += 1
                summary["total_size_mb"] += validation["size_mb"]
            else:
                summary["missing_databases"] += 1
        
        return summary
    
    def repair_database_paths(self) -> Dict[str, bool]:
        """修复数据库路径问题"""
        repair_results = {}
        
        logger.info("🔧 开始修复数据库路径问题...")
        
        # 1. 迁移旧路径
        migration_results = self.migrate_old_database_paths()
        repair_results.update(migration_results)
        
        # 2. 创建缺失的数据库
        missing_dbs = self.get_missing_databases()
        for db_name in missing_dbs:
            if self.create_database_if_not_exists(db_name):
                repair_results[f"{db_name}_created"] = True
            else:
                repair_results[f"{db_name}_created"] = False
        
        logger.info("✅ 数据库路径修复完成")
        return repair_results

# 全局实例
unified_db_path_manager = UnifiedDatabasePathManager()

# 便捷函数
def get_database_path(db_name: str) -> str:
    """便捷函数：获取数据库路径"""
    return unified_db_path_manager.get_database_path_str(db_name)

def check_database_exists(db_name: str) -> bool:
    """便捷函数：检查数据库是否存在"""
    return unified_db_path_manager.check_database_exists(db_name)

def create_database_if_not_exists(db_name: str) -> bool:
    """便捷函数：创建数据库（如果不存在）"""
    return unified_db_path_manager.create_database_if_not_exists(db_name)

def repair_all_database_paths() -> Dict[str, bool]:
    """便捷函数：修复所有数据库路径"""
    return unified_db_path_manager.repair_database_paths()

def get_database_summary() -> Dict[str, any]:
    """便捷函数：获取数据库概要"""
    return unified_db_path_manager.get_database_summary()
