#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星数据管理服务
统一管理所有数据源，提供数据质量监控和数据访问控制
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

class DataManagementService:
    """瑶光星数据管理服务"""
    
    def __init__(self):
        self.service_name = "瑶光星数据管理服务"
        self.version = "1.0.0"
        
        # 数据源配置
        self.data_sources = {
            "stock_historical": "data/stock_historical.db",
            "stock_realtime": "data/stock_realtime.db", 
            "stock_master": "data/stock_master.db",
            "yaoguang_persistence": "data/yaoguang_star/yaoguang_persistence.db"
        }
        
        # 数据质量监控
        self.data_quality_metrics = {
            "last_check": None,
            "data_completeness": {},
            "data_freshness": {},
            "data_accuracy": {}
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                "service_name": self.service_name,
                "version": self.version,
                "timestamp": datetime.now().isoformat(),
                "data_sources": {},
                "overall_health": True
            }
            
            # 检查每个数据源
            for source_name, db_path in self.data_sources.items():
                try:
                    db_file = Path(db_path)
                    if db_file.exists():
                        # 测试数据库连接
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        conn.close()
                        
                        status["data_sources"][source_name] = {
                            "status": "healthy",
                            "path": str(db_file),
                            "exists": True,
                            "tables_count": len(tables),
                            "size_mb": round(db_file.stat().st_size / 1024 / 1024, 2)
                        }
                    else:
                        status["data_sources"][source_name] = {
                            "status": "missing",
                            "path": str(db_file),
                            "exists": False,
                            "error": "数据库文件不存在"
                        }
                        status["overall_health"] = False
                        
                except Exception as e:
                    status["data_sources"][source_name] = {
                        "status": "error",
                        "path": db_path,
                        "error": str(e)
                    }
                    status["overall_health"] = False
            
            return status
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "service_name": self.service_name,
                "error": str(e),
                "overall_health": False,
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_data_quality(self) -> Dict[str, Any]:
        """检查数据质量"""
        try:
            quality_report = {
                "check_time": datetime.now().isoformat(),
                "data_sources": {},
                "overall_score": 0.0,
                "issues": []
            }
            
            total_score = 0
            source_count = 0
            
            for source_name, db_path in self.data_sources.items():
                try:
                    db_file = Path(db_path)
                    if not db_file.exists():
                        quality_report["data_sources"][source_name] = {
                            "score": 0.0,
                            "status": "missing",
                            "issues": ["数据库文件不存在"]
                        }
                        quality_report["issues"].append(f"{source_name}: 数据库文件不存在")
                        continue
                    
                    # 检查数据库内容
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 获取表信息
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    
                    source_score = 1.0  # 基础分数
                    source_issues = []
                    
                    if len(tables) == 0:
                        source_score *= 0.5
                        source_issues.append("数据库中没有表")
                    
                    # 检查数据新鲜度（如果有时间字段）
                    for table in tables:
                        table_name = table[0]
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                            row_count = cursor.fetchone()[0]
                            if row_count == 0:
                                source_issues.append(f"表 {table_name} 为空")
                                source_score *= 0.9
                        except:
                            pass
                    
                    conn.close()
                    
                    quality_report["data_sources"][source_name] = {
                        "score": source_score,
                        "status": "healthy" if source_score > 0.8 else "warning",
                        "tables_count": len(tables),
                        "issues": source_issues
                    }
                    
                    total_score += source_score
                    source_count += 1
                    
                except Exception as e:
                    quality_report["data_sources"][source_name] = {
                        "score": 0.0,
                        "status": "error",
                        "issues": [str(e)]
                    }
                    quality_report["issues"].append(f"{source_name}: {str(e)}")
            
            # 计算总体分数
            if source_count > 0:
                quality_report["overall_score"] = total_score / source_count
            
            self.data_quality_metrics["last_check"] = datetime.now()
            
            return quality_report
            
        except Exception as e:
            logger.error(f"数据质量检查失败: {e}")
            return {
                "check_time": datetime.now().isoformat(),
                "error": str(e),
                "overall_score": 0.0
            }
