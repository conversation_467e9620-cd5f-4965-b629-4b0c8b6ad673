import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { systemApi, dashboardApi, realtimeApi } from '@/api/system'

// 系统监控数据管理
export function useSystemMonitor() {
  const loading = ref(false)
  const systemHealth = ref([])
  const systemLogs = ref([])
  const agentsStatus = ref([])
  const performanceData = ref(null)

  // 获取系统健康状态
  const fetchSystemHealth = async () => {
    try {
      loading.value = true
      const response = await systemApi.getSystemHealth()
      systemHealth.value = response.data || []
    } catch (error) {
      console.error('获取系统健康状态失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取系统健康状态，请检查后端服务')
    } finally {
      loading.value = false
    }
  }

  // 获取系统日志
  const fetchSystemLogs = async (params?: { level?: string; limit?: number }) => {
    try {
      const response = await systemApi.getSystemLogs(params)
      systemLogs.value = response.data || []
    } catch (error) {
      console.error('获取系统日志失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取系统日志，请检查后端服务')
    }
  }

  // 获取智能体状态
  const fetchAgentsStatus = async () => {
    try {
      const response = await systemApi.getAgentsStatus()
      agentsStatus.value = response.data || []
    } catch (error) {
      console.error('获取智能体状态失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取智能体状态，请检查后端服务')
    }
  }

  // 获取性能数据
  const fetchPerformanceData = async () => {
    try {
      const response = await systemApi.getSystemPerformance()
      performanceData.value = response.data
    } catch (error) {
      console.error('获取性能数据失败:', error)
      // 设置空的性能数据，避免前端崩溃
      performanceData.value = {
        cpu: [],
        memory: [],
        network: [],
        status: 'error',
        message: '无法获取性能数据，请检查后端服务'
      }
    }
  }

  // 重启智能体
  const restartAgent = async (agentKey: string) => {
    try {
      await systemApi.restartAgent(agentKey)
      await fetchAgentsStatus() // 刷新状态
    } catch (error) {
      console.error('重启智能体失败:', error)
    }
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchSystemHealth(),
      fetchSystemLogs(),
      fetchAgentsStatus(),
      fetchPerformanceData()
    ])
  }

  return {
    loading,
    systemHealth,
    systemLogs,
    agentsStatus,
    performanceData,
    fetchSystemHealth,
    fetchSystemLogs,
    fetchAgentsStatus,
    fetchPerformanceData,
    restartAgent,
    refreshAll
  }
}

// 仪表板数据管理
export function useDashboard() {
  const loading = ref(false)
  const coreMetrics = ref([])
  const starsOverview = ref([])
  const dataStream = ref([])

  // 获取核心指标
  const fetchCoreMetrics = async () => {
    try {
      loading.value = true
      const response = await dashboardApi.getCoreMetrics()
      coreMetrics.value = response.data || []
    } catch (error) {
      console.error('获取核心指标失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取核心指标，请检查后端服务')
    } finally {
      loading.value = false
    }
  }

  // 获取七星概览
  const fetchStarsOverview = async () => {
    try {
      const response = await dashboardApi.getStarsOverview()
      starsOverview.value = response.data || []
    } catch (error) {
      console.error('获取七星概览失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取七星概览，请检查后端服务')
    }
  }

  // 获取实时数据流
  const fetchDataStream = async () => {
    try {
      const response = await dashboardApi.getRealTimeDataStream()
      dataStream.value = response.data || []
    } catch (error) {
      console.error('获取实时数据流失败:', error)
      // 不再使用模拟数据，抛出错误
      throw new Error('无法获取实时数据流，请检查后端服务')
    }
  }

  // 执行快捷操作
  const executeQuickAction = async (actionKey: string, params?: any) => {
    try {
      await dashboardApi.executeQuickAction(actionKey, params)
    } catch (error) {
      console.error('执行快捷操作失败:', error)
    }
  }

  // 全局刷新
  const globalRefresh = async () => {
    try {
      await dashboardApi.globalRefresh()
      await Promise.all([
        fetchCoreMetrics(),
        fetchStarsOverview(),
        fetchDataStream()
      ])
    } catch (error) {
      console.error('全局刷新失败:', error)
    }
  }

  return {
    loading,
    coreMetrics,
    starsOverview,
    dataStream,
    fetchCoreMetrics,
    fetchStarsOverview,
    fetchDataStream,
    executeQuickAction,
    globalRefresh
  }
}

// 实时数据管理
export function useRealTimeData() {
  const connected = ref(false)
  const systemStatus = reactive({
    type: 'success',
    text: '系统运行正常'
  })

  // 连接WebSocket
  const connect = () => {
    realtimeApi.connect()
  }

  // 断开WebSocket
  const disconnect = () => {
    realtimeApi.disconnect()
  }

  // 订阅系统状态更新
  const subscribeSystemStatus = () => {
    realtimeApi.subscribeSystemStatus((data: any) => {
      systemStatus.type = data.type || 'success'
      systemStatus.text = data.text || '系统运行正常'
    })
  }

  // 订阅市场数据更新
  const subscribeMarketData = (callback: Function) => {
    realtimeApi.subscribeMarketData(callback)
  }

  // 订阅交易更新
  const subscribeTradingUpdates = (callback: Function) => {
    realtimeApi.subscribeTradingUpdates(callback)
  }

  // 订阅智能体状态更新
  const subscribeAgentStatus = (callback: Function) => {
    realtimeApi.subscribeAgentStatus(callback)
  }

  // 初始化实时连接
  const initRealTime = () => {
    connect()
    subscribeSystemStatus()
  }

  // 清理实时连接
  const cleanupRealTime = () => {
    disconnect()
  }

  return {
    connected,
    systemStatus,
    connect,
    disconnect,
    subscribeSystemStatus,
    subscribeMarketData,
    subscribeTradingUpdates,
    subscribeAgentStatus,
    initRealTime,
    cleanupRealTime
  }
}

// 角色数据管理
export function useStarRole(starKey: string) {
  const loading = ref(false)
  const statistics = ref(null)
  const taskLogs = ref([])
  const results = ref(null)
  const performance = ref(null)

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      loading.value = true
      // 这里会调用真实的API，如果失败则使用现有组件的数据
      console.log(`获取${starKey}统计数据`)
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取任务日志
  const fetchTaskLogs = async () => {
    try {
      console.log(`获取${starKey}任务日志`)
    } catch (error) {
      console.error('获取任务日志失败:', error)
    }
  }

  // 获取结果数据
  const fetchResults = async () => {
    try {
      console.log(`获取${starKey}结果数据`)
    } catch (error) {
      console.error('获取结果数据失败:', error)
    }
  }

  // 获取性能数据
  const fetchPerformance = async () => {
    try {
      console.log(`获取${starKey}性能数据`)
    } catch (error) {
      console.error('获取性能数据失败:', error)
    }
  }

  // 刷新角色数据
  const refreshRoleData = async () => {
    await Promise.all([
      fetchStatistics(),
      fetchTaskLogs(),
      fetchResults(),
      fetchPerformance()
    ])
  }

  return {
    loading,
    statistics,
    taskLogs,
    results,
    performance,
    fetchStatistics,
    fetchTaskLogs,
    fetchResults,
    fetchPerformance,
    refreshRoleData
  }
}
