#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星时间控制服务
兼容层 - 将统一时间控制服务的接口暴露给API
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 导入统一时间控制服务
from .unified_time_control_service import UnifiedTimeControlService

logger = logging.getLogger(__name__)

class TimeControlService(UnifiedTimeControlService):
    """瑶光星时间控制服务 - 兼容层"""
    
    def __init__(self):
        super().__init__()
        logger.info("✅ 瑶光星时间控制服务(兼容层)初始化完成")
    
    # 以下方法用于兼容旧接口
    
    async def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        return await self.get_current_time(session_id)
    
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """列出活跃会话"""
        result = []
        for session_id, session in self.active_sessions.items():
            result.append({
                "session_id": session_id,
                "mode": session.mode.value,
                "start_time": session.start_time.isoformat(),
                "current_time": session.current_time.isoformat(),
                "end_time": session.end_time.isoformat() if session.end_time else None
            })
        return result
