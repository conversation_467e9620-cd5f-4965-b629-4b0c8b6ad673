import { request } from '../request'

/**
 * 瑶光星API接口
 * 提供瑶光星的所有功能接口，包括：
 * 1. 学习协调功能
 * 2. 回测协调功能
 * 3. 系统状态监控
 * 4. 任务执行日志
 */
export const YaoguangStarAPI = {
  /**
   * 获取瑶光星系统状态
   */
  getSystemStatus() {
    return request({
      url: '/api/roles/yaoguang/system/status',
      method: 'get'
    })
  },

  /**
   * 获取瑶光星绩效数据
   */
  getPerformanceDashboard() {
    return request({
      url: '/api/roles/yaoguang/performance/dashboard',
      method: 'get'
    })
  },

  /**
   * 获取瑶光星学习协调结果
   */
  getLearningResults(params?: any) {
    return request({
      url: '/api/roles/yaoguang/learning/results',
      method: 'get',
      params
    })
  },

  /**
   * 获取瑶光星回测协调结果
   */
  getBacktestResults(params?: any) {
    return request({
      url: '/api/roles/yaoguang/backtest/results',
      method: 'get',
      params
    })
  },

  /**
   * 获取瑶光星统计数据
   */
  getStatistics(params?: any) {
    return request({
      url: '/api/roles/yaoguang/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 获取瑶光星任务日志
   */
  getTaskLogs(params?: any) {
    return request({
      url: '/api/roles/yaoguang/task/logs',
      method: 'get',
      params
    })
  },

  /**
   * 启动学习协调流程
   */
  startLearningCoordination(data: any) {
    return request({
      url: '/api/roles/yaoguang/learning/start',
      method: 'post',
      data
    })
  },

  /**
   * 启动回测协调流程
   */
  startBacktestCoordination(data: any) {
    return request({
      url: '/api/roles/yaoguang/backtest/start',
      method: 'post',
      data
    })
  },

  /**
   * 获取六星协调状态
   */
  getSixStarsCoordinationStatus() {
    return request({
      url: '/api/roles/yaoguang/coordination/status',
      method: 'get'
    })
  },

  /**
   * 获取学习历史记录
   */
  getLearningHistory(params?: any) {
    return request({
      url: '/api/roles/yaoguang/learning/history',
      method: 'get',
      params
    })
  },

  /**
   * 获取回测历史记录
   */
  getBacktestHistory(params?: any) {
    return request({
      url: '/api/roles/yaoguang/backtest/history',
      method: 'get',
      params
    })
  },

  /**
   * 获取智能体协调指标
   */
  getAgentCoordinationMetrics() {
    return request({
      url: '/api/roles/yaoguang/coordination/metrics',
      method: 'get'
    })
  },

  /**
   * 获取瑶光星服务健康状态
   */
  getServiceHealth() {
    return request({
      url: '/api/roles/yaoguang/service/health',
      method: 'get'
    })
  },

  /**
   * 获取瑶光星业务流程统计
   */
  getBusinessFlowStats() {
    return request({
      url: '/api/roles/yaoguang/business/stats',
      method: 'get'
    })
  },

  /**
   * 获取瑶光星智能体调用统计
   */
  getAgentCallStats() {
    return request({
      url: '/api/roles/yaoguang/agent/call-stats',
      method: 'get'
    })
  }
}
