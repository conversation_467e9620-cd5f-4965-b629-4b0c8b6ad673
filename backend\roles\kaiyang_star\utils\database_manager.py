#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星统一数据库管理器
消除重复的数据库连接代码，提供统一的数据库访问接口
"""

import sqlite3
import logging
import os
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
from datetime import datetime
import pandas as pd
# 移除错误的导入路径
# from backend.shared.config.stock_filter_config import configurable_stock_filter

logger = logging.getLogger(__name__)

class KaiyangDatabaseManager:
    """开阳星统一数据库管理器"""
    
    def __init__(self):
        self.manager_name = "开阳星数据库管理器"
        self.version = "1.0.0"
        
        # 数据库路径配置（修复：使用正确的相对路径）
        self.db_paths = {
            "stock_database": "data/stock_master.db",
            "stock_master": "data/stock_master.db",
            "stock_historical": "data/stock_historical.db",
            "stock_realtime": "data/stock_realtime.db",
            "realtime_data": "data/stock_realtime.db",
            "news_database": "data/news_database.db"
        }
        
        # 连接池配置
        self.connection_timeout = 30
        self.max_retries = 3
        
        # 初始化数据库表
        self._initialize_database_tables()

        logger.info(f"✅ {self.manager_name} v{self.version} 初始化完成")

    def _initialize_database_tables(self):
        """初始化开阳星数据库表"""
        try:
            # 创建stock_basic_info表
            with self.get_connection("stock_master") as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS stock_basic_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code TEXT NOT NULL,
                        stock_name TEXT NOT NULL,
                        industry TEXT,
                        sector TEXT,
                        market_cap REAL,
                        pe_ratio REAL,
                        pb_ratio REAL,
                        roe REAL,
                        debt_ratio REAL,
                        current_price REAL,
                        volume REAL,
                        turnover_rate REAL,
                        market_type TEXT,  -- 主板/创业板/科创板
                        listing_date TEXT,
                        total_shares REAL,
                        float_shares REAL,
                        update_time TEXT NOT NULL,
                        data_source TEXT DEFAULT 'kaiyang_star',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, update_time)
                    )
                """)

                # 创建索引
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_stock_basic_code_time
                    ON stock_basic_info(stock_code, update_time DESC)
                """)

                conn.commit()

            logger.info("✅ 开阳星stock_basic_info表创建完成")

        except Exception as e:
            logger.error(f"❌ 开阳星数据库表创建失败: {e}")

    @contextmanager
    def get_connection(self, db_type: str = "stock_historical"):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            db_path = self.db_paths.get(db_type, self.db_paths["stock_historical"])
            
            if not os.path.exists(db_path):
                logger.warning(f"数据库文件不存在: {db_path}")
                raise FileNotFoundError(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path, timeout=self.connection_timeout)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    async def execute_query(self, query: str, params: tuple = (), db_type: str = "stock_historical") -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                # 获取列名
                columns = [description[0] for description in cursor.description]
                
                # 获取数据并转换为字典列表
                rows = cursor.fetchall()
                results = []
                for row in rows:
                    result_dict = {}
                    for i, value in enumerate(row):
                        result_dict[columns[i]] = value
                    results.append(result_dict)
                
                return results
                
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return []
    
    async def execute_single_query(self, query: str, params: tuple = (), db_type: str = "stock_historical") -> Optional[Dict[str, Any]]:
        """执行查询并返回单个结果"""
        results = await self.execute_query(query, params, db_type)
        return results[0] if results else None
    
    async def execute_scalar(self, query: str, params: tuple = (), db_type: str = "stock_historical") -> Any:
        """执行查询并返回单个值"""
        try:
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            logger.error(f"标量查询执行失败: {e}")
            return None
    
    async def get_stock_pool(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取股票池数据 - 使用实际存在的表"""
        # 首先尝试从daily_data表获取股票列表
        query = """
        SELECT DISTINCT stock_code,
               stock_code as stock_name,
               CASE
                   WHEN stock_code LIKE '00%' THEN 'SZ'
                   WHEN stock_code LIKE '60%' THEN 'SH'
                   ELSE 'OTHER'
               END as exchange,
               'Unknown' as industry
        FROM daily_data
        WHERE trade_date = (SELECT MAX(trade_date) FROM daily_data)
        ORDER BY stock_code
        LIMIT ?
        """
        raw_stocks = await self.execute_query(query, (limit * 3,))  # 获取更多数据用于过滤

        # 应用基础的股票过滤逻辑
        filtered_stocks = []
        for stock in raw_stocks:
            stock_code = stock.get('stock_code', '')
            # 基础过滤：排除ST股票和无效代码
            if stock_code and not stock_code.startswith('ST') and len(stock_code) == 6:
                filtered_stocks.append(stock)
                if len(filtered_stocks) >= limit:
                    break

        return filtered_stocks
    
    async def get_stock_latest_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票最新数据"""
        query = """
        SELECT stock_code, close_price, volume, pe_ratio, pb_ratio, 
               total_market_cap, turnover_rate, trade_date
        FROM daily_data 
        WHERE stock_code = ?
        ORDER BY trade_date DESC 
        LIMIT 1
        """
        return await self.execute_single_query(query, (stock_code,))
    
    async def get_stock_history(self, stock_code: str, days: int = 60) -> pd.DataFrame:
        """获取股票历史数据"""
        query = """
        SELECT trade_date, open_price, high_price, low_price, close_price, volume
        FROM daily_data 
        WHERE stock_code = ?
        ORDER BY trade_date DESC 
        LIMIT ?
        """
        
        results = await self.execute_query(query, (stock_code, days))
        
        if results:
            df = pd.DataFrame(results)
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date').reset_index(drop=True)
            return df
        else:
            return pd.DataFrame()
    
    async def get_realtime_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取实时数据"""
        query = """
        SELECT stock_code, current_price, change_pct, volume, timestamp
        FROM realtime_data 
        WHERE stock_code = ?
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        return await self.execute_single_query(query, (stock_code,), "realtime_data")
    
    async def get_database_stats(self, db_type: str = "stock_database") -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                
                # 获取表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                stats = {
                    "database_type": db_type,
                    "tables": tables,
                    "table_counts": {}
                }
                
                # 获取每个表的记录数
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        stats["table_counts"][table] = count
                    except Exception as e:
                        logger.warning(f"获取表 {table} 记录数失败: {e}")
                        stats["table_counts"][table] = 0
                
                return stats
                
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {"error": str(e)}
    
    async def check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        health_status = {
            "overall_health": True,
            "databases": {},
            "total_records": 0,
            "check_time": datetime.now().isoformat()
        }
        
        for db_type, db_path in self.db_paths.items():
            try:
                if os.path.exists(db_path):
                    stats = await self.get_database_stats(db_type)
                    total_records = sum(stats.get("table_counts", {}).values())
                    
                    health_status["databases"][db_type] = {
                        "status": "healthy",
                        "path": db_path,
                        "tables": len(stats.get("tables", [])),
                        "total_records": total_records
                    }
                    
                    health_status["total_records"] += total_records
                else:
                    health_status["databases"][db_type] = {
                        "status": "missing",
                        "path": db_path,
                        "error": "文件不存在"
                    }
                    health_status["overall_health"] = False
                    
            except Exception as e:
                health_status["databases"][db_type] = {
                    "status": "error",
                    "path": db_path,
                    "error": str(e)
                }
                health_status["overall_health"] = False
        
        return health_status

# 全局实例
kaiyang_db_manager = KaiyangDatabaseManager()
