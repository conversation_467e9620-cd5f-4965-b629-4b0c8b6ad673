<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/开阳.png" alt="开阳星" />
          </div>
          <div class="role-details">
            <h1>开阳星 - 任务执行日志</h1>
            <p>数据收集任务监控、执行状态跟踪与历史记录管理</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="status-indicators">
            <div class="indicator" :class="{ online: systemStatus.isOnline }">
              <span class="dot"></span>
              <span>{{ systemStatus.isOnline ? '任务引擎在线' : '系统离线' }}</span>
            </div>
            <div class="indicator">
              <span class="value">{{ taskStats.activeTasks }}</span>
              <span class="label">运行中任务</span>
            </div>
          </div>
          <el-switch
            v-model="autoRefresh"
            @change="toggleAutoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
          />
          <el-button type="primary" @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新任务
          </el-button>
        </div>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><DataBoard /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.totalTasks }}</div>
          <div class="metric-label">总收集任务</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.tasksTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.completedTasks }}</div>
          <div class="metric-label">已完成任务</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.completedTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.avgExecutionTime }}s</div>
          <div class="metric-label">平均执行时间</div>
          <div class="metric-change negative">
            <el-icon><ArrowDown /></el-icon>
            -{{ taskStats.timeTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card danger">
        <div class="metric-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.failedTasks }}</div>
          <div class="metric-label">失败任务</div>
          <div class="metric-change negative">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.failedTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志监控 -->
    <div class="real-time-monitor">
      <el-card>
        <template #header>
          <div class="monitor-header">
            <h3>🔄 实时任务日志</h3>
            <div class="monitor-controls">
              <el-select v-model="logFilter" @change="filterLogs" size="small">
                <el-option label="全部日志" value="all" />
                <el-option label="数据收集" value="data_collection" />
                <el-option label="技术指标计算" value="indicator_calculation" />
                <el-option label="股票选择" value="stock_selection" />
                <el-option label="系统状态" value="system_status" />
                <el-option label="错误日志" value="error" />
              </el-select>
              <el-button @click="clearRealtimeLogs" size="small" type="danger">
                <el-icon><Delete /></el-icon>
                清空日志
              </el-button>
              <el-switch
                v-model="autoScroll"
                @change="toggleAutoScroll"
                active-text="自动滚动"
                inactive-text="停止滚动"
                size="small"
              />
              <div class="connection-status" :class="{ connected: wsConnected }">
                <span class="status-dot"></span>
                {{ wsConnected ? '实时连接' : '连接断开' }}
              </div>
            </div>
          </div>
        </template>
        <div class="realtime-logs-container" ref="logsContainer">
          <div v-if="filteredRealtimeLogs.length > 0" class="logs-list">
            <div
              v-for="log in filteredRealtimeLogs"
              :key="log.id"
              class="log-entry"
              :class="[log.level, log.type]"
            >
              <div class="log-timestamp">{{ formatLogTime(log.timestamp) }}</div>
              <div class="log-type-badge" :class="log.type">
                {{ getLogTypeIcon(log.type) }} {{ getLogTypeLabel(log.type) }}
              </div>
              <div class="log-content">
                <div class="log-action">{{ log.action }}</div>
                <div v-if="log.details" class="log-details">
                  <div v-for="(value, key) in log.details" :key="key" class="detail-item">
                    <span class="detail-key">{{ key }}:</span>
                    <span class="detail-value">{{ formatDetailValue(value) }}</span>
                  </div>
                </div>
                <div v-if="log.error" class="log-error">
                  <el-icon><Warning /></el-icon>
                  {{ log.error }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-logs">
            <el-empty description="等待实时日志数据..." />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 运行中任务监控 -->
    <div class="running-tasks-monitor">
      <el-card>
        <template #header>
          <div class="monitor-header">
            <h3>运行中任务</h3>
            <div class="monitor-controls">
              <el-select v-model="taskFilter" @change="filterTasks" size="small">
                <el-option label="全部任务" value="all" />
                <el-option label="实时数据收集" value="realtime_collection" />
                <el-option label="历史数据收集" value="historical_collection" />
                <el-option label="技术指标计算" value="indicator_calculation" />
                <el-option label="股票筛选" value="stock_screening" />
              </el-select>
            </div>
          </div>
        </template>
        <div class="task-monitor-content">
          <div v-if="filteredTasks.length > 0" class="running-tasks">
            <h4>正在执行的任务 ({{ filteredTasks.length }})</h4>
            <div class="task-list">
              <div
                v-for="task in filteredTasks"
                :key="task.id"
                class="task-item running"
              >
                <div class="task-info">
                  <div class="task-name">{{ getTaskTypeLabel(task.type) }}</div>
                  <div class="task-description">{{ task.description }}</div>
                </div>
                <div class="task-progress">
                  <el-progress
                    :percentage="task.progress"
                    :status="task.progress === 100 ? 'success' : 'primary'"
                  />
                  <span class="progress-text">{{ task.progress }}%</span>
                </div>
                <div class="task-actions">
                  <el-button size="small" @click="viewTaskDetail(task.id)">详情</el-button>
                  <el-button size="small" type="danger" @click="cancelTask(task.id)">取消</el-button>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-running-tasks">
            <el-empty description="当前没有运行中的任务" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务历史记录 -->
    <div class="task-history">
      <el-card>
        <template #header>
          <div class="history-header">
            <h3>任务历史记录</h3>
            <div class="history-controls">
              <el-select v-model="taskTypeFilter" @change="filterTaskHistory" size="small">
                <el-option label="全部类型" value="all" />
                <el-option label="实时数据收集" value="realtime_collection" />
                <el-option label="历史数据收集" value="historical_collection" />
                <el-option label="技术指标计算" value="indicator_calculation" />
                <el-option label="股票筛选" value="stock_screening" />
              </el-select>
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="filterTaskHistory"
                size="small"
              />
              <el-button @click="exportTaskHistory" size="small">
                <el-icon><Download /></el-icon>
                导出历史
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="taskHistory" v-loading="historyLoading" style="width: 100%">
          <el-table-column prop="id" label="任务ID" width="120" />
          <el-table-column prop="type" label="任务类型" width="150">
            <template #default="scope">
              <el-tag
                :type="getTaskTypeColor(scope.row.type)"
                size="small"
              >
                {{ getTaskTypeLabel(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="任务描述" min-width="200" />
          <el-table-column prop="start_time" label="开始时间" width="160" />
          <el-table-column prop="end_time" label="结束时间" width="160" />
          <el-table-column prop="duration" label="执行时长" width="100">
            <template #default="scope">
              {{ scope.row.duration }}s
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusColor(scope.row.status)"
                size="small"
              >
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="success_rate" label="成功率" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.success_rate"
                :color="getSuccessRateColor(scope.row.success_rate)"
                :show-text="false"
              />
              <span class="success-rate-text">{{ scope.row.success_rate }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="data_count" label="数据量" width="100" />
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button size="small" @click="viewTaskDetail(scope.row.id)">详情</el-button>
              <el-button
                v-if="scope.row.status === 'failed'"
                size="small"
                type="danger"
                @click="deleteTask(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  ArrowUp,
  ArrowDown,
  DataBoard,
  CircleCheck,
  Clock,
  Warning,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { KaiyangStarAPI } from '@/api/roles/kaiyang-star'

// 响应式数据
const loading = ref(false)
const historyLoading = ref(false)
const autoRefresh = ref(true)
const taskFilter = ref('all')
const taskTypeFilter = ref('all')
const dateRange = ref<[Date, Date] | null>(null)

// 实时日志相关
const realtimeLogs = ref([])
const logFilter = ref('all')
const autoScroll = ref(true)
const wsConnected = ref(false)
const logsContainer = ref(null)
let websocket = null
let logIdCounter = 0

// 系统状态
const systemStatus = ref({
  isOnline: true,
  lastUpdate: new Date()
})

// 任务统计
const taskStats = ref({
  totalTasks: 0,
  completedTasks: 0,
  activeTasks: 0,
  failedTasks: 0,
  avgExecutionTime: 0,
  tasksTrend: 0,
  completedTrend: 0,
  timeTrend: 0,
  failedTrend: 0
})

// 正在运行的任务
const runningTasks = ref([])

// 任务历史记录
const taskHistory = ref([])

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const filteredTasks = computed(() => {
  if (taskFilter.value === 'all') return runningTasks.value
  return runningTasks.value.filter(task => task.type === taskFilter.value)
})

const filteredRealtimeLogs = computed(() => {
  if (logFilter.value === 'all') return realtimeLogs.value
  return realtimeLogs.value.filter(log => log.type === logFilter.value)
})

// 方法
const loadTaskStats = async () => {
  try {
    const response = await KaiyangStarAPI.getTaskLogs({ limit: 1000 })
    if (response.data && response.data.stats) {
      const stats = response.data.stats
      taskStats.value = {
        totalTasks: stats.total_tasks || 0,
        completedTasks: stats.completed || 0,
        activeTasks: stats.running || 0,
        failedTasks: stats.failed || 0,
        avgExecutionTime: stats.avg_execution_time || 0,
        tasksTrend: stats.tasks_trend || 0,
        completedTrend: stats.completed_trend || 0,
        timeTrend: stats.time_trend || 0,
        failedTrend: stats.failed_trend || 0
      }
    } else {
      // 如果没有统计数据，设置默认值
      taskStats.value = {
        totalTasks: 0,
        completedTasks: 0,
        activeTasks: 0,
        failedTasks: 0,
        avgExecutionTime: 0,
        tasksTrend: 0,
        completedTrend: 0,
        timeTrend: 0,
        failedTrend: 0
      }
    }
  } catch (error) {
    console.error('加载任务统计失败:', error)
    // 设置默认值避免页面报错
    taskStats.value = {
      totalTasks: 0,
      completedTasks: 0,
      activeTasks: 0,
      failedTasks: 0,
      avgExecutionTime: 0,
      tasksTrend: 0,
      completedTrend: 0,
      timeTrend: 0,
      failedTrend: 0
    }
  }
}

const refreshTasks = async () => {
  await Promise.all([
    loadTaskStats(),
    loadRunningTasks(),
    loadTaskHistory()
  ])
  ElMessage.success('任务数据刷新成功')
}

const loadRunningTasks = async () => {
  try {
    const response = await KaiyangStarAPI.getTaskLogs({ status: 'running' })
    if (response.data) {
      runningTasks.value = response.data.tasks || []
    }
  } catch (error) {
    console.error('加载运行中任务失败:', error)
  }
}

const loadTaskHistory = async () => {
  try {
    historyLoading.value = true
    const response = await KaiyangStarAPI.getTaskLogs({
      limit: 100,
      start_date: dateRange.value?.[0],
      end_date: dateRange.value?.[1],
      type: taskTypeFilter.value === 'all' ? undefined : taskTypeFilter.value
    })

    if (response.data) {
      taskHistory.value = response.data.tasks || []
    }
  } catch (error) {
    console.error('加载任务历史失败:', error)
    ElMessage.error('加载任务历史失败')
  } finally {
    historyLoading.value = false
  }
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer = setInterval(refreshTasks, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

const viewTaskDetail = (taskId: string) => {
  ElMessage.info(`查看任务详情: ${taskId}`)
}

const cancelTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
      type: 'warning'
    })
    ElMessage.success('任务已取消')
    await refreshTasks()
  } catch (error) {
    // 用户取消操作
  }
}

const deleteTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个失败的任务记录吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('任务记录已删除')
    await loadTaskHistory()
  } catch (error) {
    // 用户取消操作
  }
}

const exportTaskHistory = () => {
  ElMessage.success('任务历史导出成功')
}

const filterTasks = () => {
  // 过滤逻辑已在计算属性中实现
}

const filterTaskHistory = () => {
  loadTaskHistory()
}

// 辅助方法
const getTaskTypeLabel = (type: string) => {
  const labels = {
    'realtime_collection': '实时数据收集',
    'historical_collection': '历史数据收集',
    'indicator_calculation': '技术指标计算',
    'stock_screening': '股票筛选'
  }
  return labels[type] || type
}

const getTaskTypeColor = (type: string) => {
  const colors = {
    'realtime_collection': 'primary',
    'historical_collection': 'success',
    'indicator_calculation': 'warning',
    'stock_screening': 'info'
  }
  return colors[type] || 'default'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return labels[status] || status
}

const getStatusColor = (status: string) => {
  const colors = {
    'running': 'primary',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'warning'
  }
  return colors[status] || 'default'
}

const getSuccessRateColor = (rate: number) => {
  if (rate >= 95) return '#67c23a'
  if (rate >= 85) return '#e6a23c'
  return '#f56c6c'
}

// 实时日志相关方法（已禁用WebSocket，使用轮询代替）
const connectWebSocket = () => {
  // WebSocket连接已禁用，使用轮询代替
  console.log('WebSocket连接已禁用，使用轮询获取实时日志')
}

const handleRealtimeLog = (logData) => {
  // 添加唯一ID和格式化
  const log = {
    ...logData,
    id: ++logIdCounter,
    timestamp: logData.timestamp || new Date().toISOString()
  }

  // 添加到日志列表
  realtimeLogs.value.push(log)

  // 限制日志数量，保留最新的1000条
  if (realtimeLogs.value.length > 1000) {
    realtimeLogs.value.shift()
  }

  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

const scrollToBottom = () => {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

const clearRealtimeLogs = () => {
  realtimeLogs.value = []
  ElMessage.success('实时日志已清空')
}

const toggleAutoScroll = (enabled: boolean) => {
  autoScroll.value = enabled
  if (enabled) {
    scrollToBottom()
  }
}

const filterLogs = () => {
  // 过滤逻辑已在计算属性中实现
}

const formatLogTime = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLogTypeIcon = (type: string) => {
  const icons = {
    'data_collection': '📊',
    'indicator_calculation': '🔧',
    'stock_selection': '🎯',
    'system_status': '⚙️',
    'error': '❌'
  }
  return icons[type] || '📝'
}

const getLogTypeLabel = (type: string) => {
  const labels = {
    'data_collection': '数据收集',
    'indicator_calculation': '指标计算',
    'stock_selection': '股票选择',
    'system_status': '系统状态',
    'error': '错误'
  }
  return labels[type] || type
}

const formatDetailValue = (value: any) => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

onMounted(() => {
  refreshTasks()
  if (autoRefresh.value) {
    toggleAutoRefresh(true)
  }
  // WebSocket连接已禁用
  // connectWebSocket()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  // 关闭WebSocket连接
  if (websocket) {
    websocket.close()
  }
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 开阳星任务日志特定样式 */
.role-page-container {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.page-header {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
}

.role-details h1 {
  color: white;
}

.role-details p {
  color: rgba(255, 255, 255, 0.9);
}

.status-indicators {
  display: flex;
  gap: 16px;
  align-items: center;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 14px;
}

.indicator.online .dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator .value {
  font-weight: 600;
  font-size: 16px;
}

.indicator .label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 实时日志监控区域 */
.real-time-monitor {
  margin-bottom: 24px;
}

.realtime-logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #fafafa;
}

.logs-list {
  padding: 8px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #e5e7eb;
  font-size: 13px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-entry.info {
  border-left-color: #3b82f6;
}

.log-entry.success {
  border-left-color: #10b981;
}

.log-entry.warning {
  border-left-color: #f59e0b;
}

.log-entry.error {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.log-timestamp {
  color: #6b7280;
  font-size: 11px;
  min-width: 60px;
  font-family: monospace;
}

.log-type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.log-type-badge.data_collection {
  background: #dbeafe;
  color: #1d4ed8;
}

.log-type-badge.indicator_calculation {
  background: #fef3c7;
  color: #92400e;
}

.log-type-badge.stock_selection {
  background: #d1fae5;
  color: #065f46;
}

.log-type-badge.system_status {
  background: #e0e7ff;
  color: #3730a3;
}

.log-type-badge.error {
  background: #fee2e2;
  color: #991b1b;
}

.log-content {
  flex: 1;
}

.log-action {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.log-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.detail-item {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.detail-key {
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  color: #1f2937;
  margin-left: 4px;
}

.log-error {
  color: #dc2626;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #fee2e2;
  color: #991b1b;
}

.connection-status.connected {
  background: #d1fae5;
  color: #065f46;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ef4444;
}

.connection-status.connected .status-dot {
  background: #10b981;
  animation: pulse 2s infinite;
}

.no-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6b7280;
}

/* 运行中任务监控区域 */
.running-tasks-monitor {
  margin-bottom: 24px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.monitor-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-monitor-content {
  min-height: 200px;
}

.running-tasks h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.task-item.running {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.task-description {
  font-size: 12px;
  color: #6b7280;
}

.task-progress {
  width: 200px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.no-running-tasks {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 任务历史区域 */
.task-history {
  margin-bottom: 24px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.history-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.success-rate-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 8px;
  }

  .monitor-header,
  .history-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .monitor-controls,
  .history-controls {
    width: 100%;
    justify-content: flex-start;
  }

  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .task-progress {
    width: 100%;
  }
}
</style>