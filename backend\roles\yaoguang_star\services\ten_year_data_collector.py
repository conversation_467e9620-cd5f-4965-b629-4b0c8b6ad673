#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星十年数据收集器
专注于收集和整合10年历史数据，用于深度学习和回测
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sqlite3
from pathlib import Path
import pandas as pd

logger = logging.getLogger(__name__)

class TenYearDataCollector:
    """瑶光星十年数据收集器"""
    
    def __init__(self):
        self.service_name = "瑶光星十年数据收集器"
        self.version = "1.0.0"
        
        # 数据库路径
        self.db_paths = {
            "historical": Path("data/stock_historical.db"),
            "realtime": Path("data/stock_realtime.db"),
            "master": Path("data/stock_master.db")
        }
        
        # 收集配置
        self.collection_config = {
            "default_years": 10,
            "batch_size": 100,
            "parallel_tasks": 5,
            "retry_count": 3,
            "data_types": ["daily", "financial", "industry"]
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def collect_historical_data(self, 
                                    stock_codes: List[str],
                                    years: int = None,
                                    data_types: List[str] = None) -> Dict[str, Any]:
        """收集历史数据"""
        try:
            years = years or self.collection_config["default_years"]
            data_types = data_types or self.collection_config["data_types"]
            
            start_date = (datetime.now() - timedelta(days=365 * years)).strftime('%Y-%m-%d')
            end_date = datetime.now().strftime('%Y-%m-%d')
            
            logger.info(f"📊 开始收集 {len(stock_codes)} 只股票的 {years} 年历史数据")
            
            results = {
                "success": True,
                "collection_id": f"hist_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "start_date": start_date,
                "end_date": end_date,
                "stock_count": len(stock_codes),
                "years": years,
                "data_types": data_types,
                "results": {}
            }
            
            # 分批处理
            batches = [stock_codes[i:i + self.collection_config["batch_size"]] 
                      for i in range(0, len(stock_codes), self.collection_config["batch_size"])]
            
            for batch_idx, batch in enumerate(batches):
                logger.info(f"📊 处理批次 {batch_idx + 1}/{len(batches)}")
                
                # 并行收集数据
                tasks = []
                for data_type in data_types:
                    task = asyncio.create_task(
                        self._collect_data_by_type(batch, data_type, start_date, end_date)
                    )
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks)
                
                # 合并结果
                for data_type, type_result in zip(data_types, batch_results):
                    if data_type not in results["results"]:
                        results["results"][data_type] = type_result
                    else:
                        results["results"][data_type]["successful_stocks"].extend(type_result["successful_stocks"])
                        results["results"][data_type]["failed_stocks"].extend(type_result["failed_stocks"])
            
            # 计算总体统计
            total_successful = sum(len(results["results"][dt]["successful_stocks"]) for dt in data_types)
            total_failed = sum(len(results["results"][dt]["failed_stocks"]) for dt in data_types)
            
            results["total_successful"] = total_successful
            results["total_failed"] = total_failed
            results["success_rate"] = total_successful / (total_successful + total_failed) if (total_successful + total_failed) > 0 else 0
            
            logger.info(f"✅ 历史数据收集完成: 成功率 {results['success_rate']:.2%}")
            
            return results
            
        except Exception as e:
            logger.error(f"历史数据收集失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _collect_data_by_type(self, 
                                  stock_codes: List[str],
                                  data_type: str,
                                  start_date: str,
                                  end_date: str) -> Dict[str, Any]:
        """按类型收集数据"""
        result = {
            "data_type": data_type,
            "successful_stocks": [],
            "failed_stocks": []
        }
        
        for stock_code in stock_codes:
            try:
                # 模拟数据收集
                await asyncio.sleep(0.01)  # 模拟网络延迟
                
                # 在实际实现中，这里应该调用真实的数据API
                # 例如：data = await self._fetch_real_data(stock_code, data_type, start_date, end_date)
                
                # 模拟成功
                result["successful_stocks"].append(stock_code)
                
            except Exception as e:
                logger.warning(f"收集 {stock_code} 的 {data_type} 数据失败: {e}")
                result["failed_stocks"].append({
                    "stock_code": stock_code,
                    "error": str(e)
                })
        
        return result
    
    async def check_data_completeness(self, stock_codes: List[str]) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            results = {
                "check_time": datetime.now().isoformat(),
                "stock_count": len(stock_codes),
                "completeness": {},
                "overall_score": 0.0
            }
            
            # 检查每个数据库
            for db_name, db_path in self.db_paths.items():
                if not db_path.exists():
                    results["completeness"][db_name] = {
                        "exists": False,
                        "score": 0.0,
                        "message": "数据库不存在"
                    }
                    continue
                
                # 连接数据库
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]
                
                if not tables:
                    results["completeness"][db_name] = {
                        "exists": True,
                        "tables": 0,
                        "score": 0.1,
                        "message": "数据库存在但没有表"
                    }
                    conn.close()
                    continue
                
                # 简单检查每个表的数据
                table_stats = {}
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        table_stats[table] = count
                    except:
                        table_stats[table] = -1  # 表存在但无法查询
                
                # 计算完整性分数
                total_records = sum(count for count in table_stats.values() if count > 0)
                empty_tables = sum(1 for count in table_stats.values() if count == 0)
                
                completeness_score = 0.0
                if len(tables) > 0:
                    # 基础分 0.5
                    completeness_score = 0.5
                    
                    # 根据记录数加分
                    if total_records > 0:
                        completeness_score += 0.3
                    
                    # 根据空表比例减分
                    if empty_tables > 0:
                        completeness_score -= (empty_tables / len(tables)) * 0.2
                
                results["completeness"][db_name] = {
                    "exists": True,
                    "tables": len(tables),
                    "records": total_records,
                    "empty_tables": empty_tables,
                    "score": completeness_score,
                    "message": "数据库检查完成"
                }
                
                conn.close()
            
            # 计算总体分数
            if self.db_paths:
                total_score = sum(db_info.get("score", 0.0) for db_info in results["completeness"].values())
                results["overall_score"] = total_score / len(self.db_paths)
            
            return results
            
        except Exception as e:
            logger.error(f"数据完整性检查失败: {e}")
            return {
                "check_time": datetime.now().isoformat(),
                "error": str(e),
                "overall_score": 0.0
            }

# 全局实例
ten_year_data_collector = TenYearDataCollector()
