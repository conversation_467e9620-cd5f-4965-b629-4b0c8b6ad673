#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多源数据管理器 - 修复版本
整合多个数据源，提供统一的股票数据获取接口
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class MultiSourceDataManager:
    """多源数据管理器"""
    
    def __init__(self):
        self.name = "多源数据管理器"
        self.version = "2.0.0"
        
        # API端点配置
        self.api_endpoints = {
            'eastmoney': 'https://push2.eastmoney.com/api/qt/ulist/get',
            'tencent': 'https://qt.gtimg.cn/q=',
            # 'sina': 'https://hq.sinajs.cn/list='  # 已移除不稳定的新浪API
        }
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """获取股票数据"""
        try:
            # 优先使用东方财富API获取数据
            eastmoney_data = await self._get_eastmoney_data(symbol)
            if eastmoney_data.get("success"):
                return eastmoney_data

            # 如果东方财富失败，尝试腾讯API
            tencent_data = await self._get_tencent_data(symbol)
            if tencent_data.get("success"):
                return tencent_data

            # 如果都失败，返回错误而不是备用数据
            return {"success": False, "error": f"无法从任何数据源获取股票{symbol}的数据"}

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return {"success": False, "error": f"获取股票数据失败: {str(e)}"}
    
    async def _get_eastmoney_data(self, symbol: str) -> Dict[str, Any]:
        """从东方财富获取数据 - 使用真实API接口"""
        try:
            # 转换股票代码格式
            if symbol.startswith('0') or symbol.startswith('3'):
                secid = f"0.{symbol}"
            else:
                secid = f"1.{symbol}"

            # 使用真实的东方财富API
            url = self.api_endpoints['eastmoney']
            params = {
                'fltt': '1',
                'invt': '2',
                'fields': 'f2,f3,f4,f5,f6,f12,f13,f14,f15,f16,f17,f18,f20,f21,f350,f351',
                'secids': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'pn': '1',
                'np': '1',
                'pz': '1',
                'dect': '1'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        text = await response.text()
                        
                        # 处理JSONP格式响应
                        if 'jQuery' in text and '(' in text:
                            # 提取JSON部分
                            start = text.find('(') + 1
                            end = text.rfind(')')
                            json_str = text[start:end]
                            data = json.loads(json_str)
                        else:
                            data = await response.json()
                        
                        if data.get('rc') == 0 and data.get('data', {}).get('diff'):
                            stock_list = data['data']['diff']
                            if stock_list:
                                stock_data = stock_list[0]  # 取第一个股票数据
                                
                                return {
                                    "success": True,
                                    "data_source": "eastmoney_api",
                                    "data": {
                                        "symbol": symbol,
                                        "name": stock_data.get('f14', ''),  # 股票名称
                                        "current_price": stock_data.get('f2', 0) / 100,  # 当前价格
                                        "change_percent": stock_data.get('f3', 0) / 100,  # 涨跌幅
                                        "change_amount": stock_data.get('f4', 0) / 100,  # 涨跌额
                                        "volume": stock_data.get('f5', 0),  # 成交量
                                        "turnover": stock_data.get('f6', 0),  # 成交额
                                        "high": stock_data.get('f15', 0) / 100,  # 最高价
                                        "low": stock_data.get('f16', 0) / 100,  # 最低价
                                        "open": stock_data.get('f17', 0) / 100,  # 今开
                                        "close_yesterday": stock_data.get('f18', 0) / 100,  # 昨收
                                        "market_cap": stock_data.get('f20', 0),  # 总市值
                                        "circulation_market_cap": stock_data.get('f21', 0),  # 流通市值
                                        "limit_up": stock_data.get('f350', 0) / 100,  # 涨停价
                                        "limit_down": stock_data.get('f351', 0) / 100,  # 跌停价
                                        "timestamp": datetime.now().isoformat()
                                    }
                                }

            return {"success": False, "error": "东方财富API返回数据为空"}

        except Exception as e:
            logger.error(f"东方财富数据获取失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_tencent_data(self, symbol: str) -> Dict[str, Any]:
        """从腾讯财经获取数据"""
        try:
            # 转换股票代码格式
            if symbol.startswith('0') or symbol.startswith('3'):
                tencent_symbol = f"sz{symbol}"
            else:
                tencent_symbol = f"sh{symbol}"

            url = f"{self.api_endpoints['tencent']}{tencent_symbol}"

            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=5) as response:
                    if response.status == 200:
                        content = await response.text()
                        if 'v_' in content:
                            # 解析腾讯数据格式
                            data_str = content.split('"')[1]
                            data_parts = data_str.split('~')

                            if len(data_parts) >= 50:
                                return {
                                    "success": True,
                                    "data_source": "tencent_api",
                                    "data": {
                                        "symbol": symbol,
                                        "name": data_parts[1],
                                        "current_price": float(data_parts[3]),
                                        "change_percent": float(data_parts[5]),
                                        "open": float(data_parts[5]),
                                        "high": float(data_parts[33]),
                                        "low": float(data_parts[34]),
                                        "volume": int(data_parts[36]),
                                        "timestamp": datetime.now().isoformat()
                                    }
                                }

            return {"success": False, "error": "腾讯数据解析失败"}

        except Exception as e:
            logger.error(f"腾讯数据获取失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _calculate_change_from_data(self, current_price: float, previous_price: float) -> float:
        """从真实数据计算价格变化"""
        try:
            if previous_price and previous_price > 0:
                return (current_price - previous_price) / previous_price
            return 0.0
        except Exception:
            return 0.0
    
    async def _get_fallback_data(self, symbol: str) -> Dict[str, Any]:
        """获取备用数据（从本地数据库获取真实历史数据）"""
        try:
            import sqlite3
            from pathlib import Path

            # 连接到本地数据库
            db_path = Path('backend/data/stock_master.db')
            if not db_path.exists():
                return {"success": False, "error": "本地数据库不存在"}

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取股票基本信息
            cursor.execute(
                "SELECT stock_name, industry FROM stock_info WHERE stock_code = ?",
                (symbol,)
            )
            stock_info = cursor.fetchone()

            if not stock_info:
                conn.close()
                return {"success": False, "error": f"股票{symbol}不存在于数据库"}

            stock_name, industry = stock_info

            # 获取最新的历史数据
            cursor.execute("""
                SELECT open_price, close_price, high_price, low_price, volume, amount,
                       change_percent, total_market_cap, float_market_cap, limit_up, limit_down
                FROM daily_data
                WHERE stock_code = ?
                ORDER BY trade_date DESC
                LIMIT 1
            """, (symbol,))

            latest_data = cursor.fetchone()
            conn.close()

            if not latest_data:
                return {"success": False, "error": f"股票{symbol}无历史数据"}

            (open_price, close_price, high_price, low_price, volume, amount,
             change_percent, total_market_cap, float_market_cap, limit_up, limit_down) = latest_data

            return {
                "success": True,
                "data_source": "local_database",
                "data": {
                    "symbol": symbol,
                    "name": stock_name or f"股票{symbol}",
                    "current_price": close_price or 0,
                    "change_percent": change_percent or 0,
                    "change_amount": (close_price - open_price) if (close_price and open_price) else 0,
                    "volume": volume or 0,
                    "turnover": amount or 0,
                    "high": high_price or 0,
                    "low": low_price or 0,
                    "open": open_price or 0,
                    "close_yesterday": open_price or 0,  # 简化处理
                    "market_cap": total_market_cap or 0,
                    "circulation_market_cap": float_market_cap or 0,
                    "limit_up": limit_up or 0,
                    "limit_down": limit_down or 0,
                    "industry": industry or "未知",
                    "timestamp": datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"本地数据获取失败: {e}")
            return {"success": False, "error": str(e)}

# 全局实例
multi_source_data_manager = MultiSourceDataManager()
