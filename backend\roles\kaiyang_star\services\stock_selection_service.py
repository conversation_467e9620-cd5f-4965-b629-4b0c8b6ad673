"""
开阳星股票选择服务
负责根据各种条件筛选和选择股票
"""

import sqlite3
import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class StockSelectionService:
    """股票选择服务"""
    
    def __init__(self):
        """初始化股票选择服务"""
        self.stock_db_path = "data/stock_master.db"
        self.realtime_db_path = "data/stock_realtime.db"  # 使用有数据的新数据库
        self.sector_db_path = "backend/roles/backend/roles/data/sector_database.db"
        
        logger.info("✅ 股票选择服务 v1.0.0 初始化完成")
    
    async def select_stocks(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据条件筛选股票
        
        Args:
            filters: 筛选条件
                - exclude_st: 是否排除ST股票
                - include_chinext: 是否包含创业板
                - min_market_cap: 最小市值(万元)
                - max_market_cap: 最大市值(万元)
                - min_price: 最小价格
                - max_price: 最大价格
                - sectors: 指定板块列表
                - min_volume: 最小成交量
                - limit: 返回数量限制
                - sort_by: 排序字段
                - sort_order: 排序方向(asc/desc)
        
        Returns:
            Dict: 筛选结果
        """
        try:
            # 分别查询股票基础信息和实时数据，然后合并
            # 1. 先从股票数据库获取基础信息
            stock_conn = sqlite3.connect(self.stock_db_path)
            stock_cursor = stock_conn.cursor()

            # 首先检查表是否存在
            stock_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_basic_info'")
            if not stock_cursor.fetchone():
                # 尝试使用stock_basic表
                stock_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_basic'")
                if stock_cursor.fetchone():
                    base_query = """
                        SELECT stock_code, stock_name, industry, sector,
                               market_cap, pe_ratio, pb_ratio
                        FROM stock_basic
                        WHERE 1=1
                    """
                else:
                    # 如果都没有，返回空结果
                    logger.warning("❌ 数据库中没有找到股票基础信息表")
                    return {
                        "success": False,
                        "error": "数据库中没有找到股票基础信息表",
                        "selected_stocks": []
                    }
            else:
                base_query = """
                    SELECT stock_code, stock_name, industry, sector,
                           market_cap, pe_ratio, pb_ratio
                    FROM stock_basic_info
                    WHERE 1=1
                """
            
            conditions = []
            params = []
            
            # ST股票过滤
            if filters.get("exclude_st", True):
                conditions.append("AND (s.stock_name NOT LIKE '%ST%' OR s.stock_name IS NULL)")
            
            # 创业板过滤
            if not filters.get("include_chinext", True):
                conditions.append("AND (s.stock_code NOT LIKE '300%')")
            
            # 市值过滤
            if filters.get("min_market_cap"):
                conditions.append("AND s.total_market_cap >= ?")
                params.append(filters["min_market_cap"])
            
            if filters.get("max_market_cap"):
                conditions.append("AND s.total_market_cap <= ?")
                params.append(filters["max_market_cap"])
            
            # 价格过滤
            if filters.get("min_price"):
                conditions.append("AND r.current_price >= ?")
                params.append(filters["min_price"])
            
            if filters.get("max_price"):
                conditions.append("AND r.current_price <= ?")
                params.append(filters["max_price"])
            
            # 成交量过滤
            if filters.get("min_volume"):
                conditions.append("AND r.volume >= ?")
                params.append(filters["min_volume"])
            
            # 板块过滤
            if filters.get("sectors"):
                sector_placeholders = ",".join(["?" for _ in filters["sectors"]])
                conditions.append(f"AND s.sector IN (SELECT sector_name FROM sector_info WHERE sector_code IN ({sector_placeholders}))")
                params.extend(filters["sectors"])
            
            # 组合查询
            query = base_query + " ".join(conditions)
            
            # 排序
            sort_by = filters.get("sort_by", "total_market_cap")
            sort_order = filters.get("sort_order", "desc").upper()
            
            # 映射排序字段
            sort_field_map = {
                "market_cap": "s.total_market_cap",
                "total_market_cap": "s.total_market_cap",
                "price": "r.current_price",
                "current_price": "r.current_price",
                "change_pct": "r.change_pct",
                "volume": "r.volume",
                "pe_ratio": "s.pe_ratio",
                "pb_ratio": "s.pb_ratio"
            }
            
            sort_field = sort_field_map.get(sort_by, "s.total_market_cap")
            query += f" ORDER BY {sort_field} {sort_order}"
            
            # 限制数量
            limit = filters.get("limit", 100)
            query += f" LIMIT {limit}"
            
            # 执行查询
            conn = sqlite3.connect(self.stock_db_path)
            cursor = conn.cursor()
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # 格式化结果
            stocks = []
            for row in results:
                stock = {
                    "stock_code": row[0],
                    "stock_name": row[1],
                    "industry": row[2],
                    "sector": row[3],
                    "market_cap": row[4],
                    "pe_ratio": row[5],
                    "pb_ratio": row[6],
                    "current_price": row[7],
                    "change_pct": row[8],
                    "volume": row[9],
                    "amount": row[10]
                }
                stocks.append(stock)
            
            conn.close()
            
            return {
                "success": True,
                "stocks": stocks,
                "total_count": len(stocks),
                "filters_applied": filters
            }
        
        except Exception as e:
            logger.error(f"❌ 股票筛选失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stocks": []
            }
    
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取单只股票详细信息
        
        Args:
            stock_code: 股票代码
        
        Returns:
            Dict: 股票信息
        """
        try:
            query = """
                SELECT s.stock_code, s.stock_name, s.industry, s.sector,
                       s.listing_date, s.market_cap, s.float_shares,
                       s.pe_ratio, s.pb_ratio, s.update_time,
                       r.current_price, r.change_amount, r.change_pct,
                       r.volume, r.amount, r.update_time as realtime_update
                FROM stock_basic_info s
                LEFT JOIN realtime_data r ON s.stock_code = r.stock_code
                WHERE s.stock_code = ?
            """
            
            conn = sqlite3.connect(self.stock_db_path)
            cursor = conn.cursor()
            
            cursor.execute(query, (stock_code,))
            result = cursor.fetchone()
            
            if result:
                stock_info = {
                    "stock_code": result[0],
                    "stock_name": result[1],
                    "industry": result[2],
                    "sector": result[3],
                    "listing_date": result[4],
                    "market_cap": result[5],
                    "float_shares": result[6],
                    "pe_ratio": result[7],
                    "pb_ratio": result[8],
                    "basic_update_time": result[9],
                    "current_price": result[10],
                    "change_amount": result[11],
                    "change_pct": result[12],
                    "volume": result[13],
                    "amount": result[14],
                    "realtime_update_time": result[15]
                }
                
                conn.close()
                
                return {
                    "success": True,
                    "stock_info": stock_info
                }
            else:
                conn.close()
                return {
                    "success": False,
                    "error": f"股票 {stock_code} 不存在"
                }
        
        except Exception as e:
            logger.error(f"❌ 获取股票信息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def search_stocks(self, query: str, limit: int = 20) -> Dict[str, Any]:
        """
        搜索股票
        
        Args:
            query: 搜索关键词(股票代码或名称)
            limit: 返回数量限制
        
        Returns:
            Dict: 搜索结果
        """
        try:
            search_query = """
                SELECT stock_code, stock_name, industry, sector, market_cap
                FROM stock_basic_info
                WHERE stock_code LIKE ? OR stock_name LIKE ?
                ORDER BY market_cap DESC
                LIMIT ?
            """
            
            search_pattern = f"%{query}%"
            
            conn = sqlite3.connect(self.stock_db_path)
            cursor = conn.cursor()
            
            cursor.execute(search_query, (search_pattern, search_pattern, limit))
            results = cursor.fetchall()
            
            stocks = []
            for row in results:
                stock = {
                    "stock_code": row[0],
                    "stock_name": row[1],
                    "industry": row[2],
                    "sector": row[3],
                    "market_cap": row[4]
                }
                stocks.append(stock)
            
            conn.close()
            
            return {
                "success": True,
                "stocks": stocks,
                "query": query,
                "total_count": len(stocks)
            }
        
        except Exception as e:
            logger.error(f"❌ 股票搜索失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stocks": []
            }
    
    def get_sector_stocks(self, sector_code: str, limit: int = 50) -> Dict[str, Any]:
        """
        获取指定板块的股票
        
        Args:
            sector_code: 板块代码
            limit: 返回数量限制
        
        Returns:
            Dict: 板块股票列表
        """
        try:
            if not os.path.exists(self.sector_db_path):
                return {
                    "success": False,
                    "error": "板块数据库不存在"
                }
            
            # 从板块数据库获取成分股
            sector_conn = sqlite3.connect(self.sector_db_path)
            sector_cursor = sector_conn.cursor()
            
            sector_cursor.execute("""
                SELECT stock_code, stock_name, sector_name, is_leading, weight
                FROM stock_sector_mapping
                WHERE sector_code = ?
                ORDER BY is_leading DESC, weight DESC
                LIMIT ?
            """, (sector_code, limit))
            
            sector_results = sector_cursor.fetchall()
            sector_conn.close()
            
            if not sector_results:
                return {
                    "success": False,
                    "error": f"板块 {sector_code} 没有成分股数据"
                }
            
            # 获取股票详细信息
            stock_codes = [row[0] for row in sector_results]
            placeholders = ",".join(["?" for _ in stock_codes])
            
            stock_query = f"""
                SELECT s.stock_code, s.stock_name, s.total_market_cap, s.pe_ratio,
                       r.current_price, r.change_pct, r.volume
                FROM stock_basic_info s
                LEFT JOIN realtime_data r ON s.stock_code = r.stock_code
                WHERE s.stock_code IN ({placeholders})
            """
            
            conn = sqlite3.connect(self.stock_db_path)
            cursor = conn.cursor()
            
            cursor.execute(stock_query, stock_codes)
            stock_results = cursor.fetchall()
            conn.close()
            
            # 合并数据
            stock_dict = {row[0]: row for row in stock_results}
            
            stocks = []
            for sector_row in sector_results:
                stock_code = sector_row[0]
                stock_data = stock_dict.get(stock_code)
                
                if stock_data:
                    stock = {
                        "stock_code": stock_code,
                        "stock_name": sector_row[1],
                        "sector_name": sector_row[2],
                        "is_leading": bool(sector_row[3]),
                        "weight": sector_row[4],
                        "total_market_cap": stock_data[2],
                        "pe_ratio": stock_data[3],
                        "current_price": stock_data[4],
                        "change_pct": stock_data[5],
                        "volume": stock_data[6]
                    }
                    stocks.append(stock)
            
            return {
                "success": True,
                "sector_code": sector_code,
                "stocks": stocks,
                "total_count": len(stocks)
            }
        
        except Exception as e:
            logger.error(f"❌ 获取板块股票失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stocks": []
            }
    
    def get_database_status(self) -> Dict[str, Any]:
        """
        获取数据库状态
        
        Returns:
            Dict: 数据库状态信息
        """
        try:
            status = {}
            
            # 股票数据库状态
            if os.path.exists(self.stock_db_path):
                conn = sqlite3.connect(self.stock_db_path)
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM stock_basic_info')
                total_stocks = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM stock_basic_info WHERE stock_name IS NOT NULL AND stock_name != "" AND NOT stock_name LIKE "股票%"')
                valid_names = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM stock_basic_info WHERE industry_sector IS NOT NULL AND industry_sector != ""')
                with_sectors = cursor.fetchone()[0]
                
                conn.close()
                
                status["stock_database"] = {
                    "total_stocks": total_stocks,
                    "valid_names": valid_names,
                    "with_sectors": with_sectors,
                    "name_completeness": valid_names / total_stocks * 100 if total_stocks > 0 else 0,
                    "sector_completeness": with_sectors / total_stocks * 100 if total_stocks > 0 else 0
                }
            
            # 实时数据库状态
            if os.path.exists(self.realtime_db_path):
                conn = sqlite3.connect(self.realtime_db_path)
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM realtime_data')
                realtime_records = cursor.fetchone()[0]
                
                cursor.execute('SELECT MAX(update_time) FROM realtime_data')
                latest_update = cursor.fetchone()[0]
                
                conn.close()
                
                status["realtime_database"] = {
                    "total_records": realtime_records,
                    "latest_update": latest_update
                }
            
            # 板块数据库状态
            if os.path.exists(self.sector_db_path):
                conn = sqlite3.connect(self.sector_db_path)
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM sector_info WHERE sector_type = "行业板块"')
                industry_sectors = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM stock_sector_mapping')
                mappings = cursor.fetchone()[0]
                
                conn.close()
                
                status["sector_database"] = {
                    "industry_sectors": industry_sectors,
                    "total_mappings": mappings
                }
            
            return {
                "success": True,
                "status": status,
                "check_time": datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"❌ 获取数据库状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# 全局实例
stock_selection_service = StockSelectionService()
