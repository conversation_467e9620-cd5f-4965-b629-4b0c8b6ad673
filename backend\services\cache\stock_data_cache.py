#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据缓存服务
提供高性能的数据缓存和快速访问
"""


# 添加当前目录到Python路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.database_config import get_database_path

import asyncio
import json
import logging
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
from collections import OrderedDict

logger = logging.getLogger(__name__)

class StockDataCache:
    """股票数据缓存服务"""
    
    def __init__(self, max_size: int = 10000, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = OrderedDict()
        self.access_times = {}
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
        
        logger.info(f"✅ 股票数据缓存初始化完成，最大容量: {max_size}, TTL: {ttl_seconds}秒")
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存是否过期"""
        if key not in self.access_times:
            return True
        
        return time.time() - self.access_times[key] > self.ttl_seconds
    
    def _evict_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, access_time in self.access_times.items()
            if current_time - access_time > self.ttl_seconds
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.access_times.pop(key, None)
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        while len(self.cache) >= self.max_size:
            oldest_key = next(iter(self.cache))
            self.cache.pop(oldest_key)
            self.access_times.pop(oldest_key, None)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        with self.lock:
            if key in self.cache and not self._is_expired(key):
                # 命中缓存，更新访问时间和位置
                value = self.cache.pop(key)
                self.cache[key] = value
                self.access_times[key] = time.time()
                self.hit_count += 1
                return value
            else:
                # 缓存未命中
                self.miss_count += 1
                return None
    
    def set(self, key: str, value: Any):
        """设置缓存数据"""
        with self.lock:
            # 清理过期缓存
            self._evict_expired()
            
            # LRU淘汰
            self._evict_lru()
            
            # 添加新数据
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def delete(self, key: str):
        """删除缓存数据"""
        with self.lock:
            self.cache.pop(key, None)
            self.access_times.pop(key, None)
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            
            return {
                "cache_size": len(self.cache),
                "max_size": self.max_size,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "ttl_seconds": self.ttl_seconds
            }

class StockDataCacheService:
    """股票数据缓存服务"""
    
    def __init__(self):
        self.service_name = "StockDataCacheService"
        self.version = "1.0.0"
        
        # 不同类型数据的缓存
        self.stock_info_cache = StockDataCache(max_size=10000, ttl_seconds=3600)  # 1小时
        self.daily_data_cache = StockDataCache(max_size=5000, ttl_seconds=300)    # 5分钟
        self.technical_cache = StockDataCache(max_size=3000, ttl_seconds=600)     # 10分钟
        self.realtime_cache = StockDataCache(max_size=2000, ttl_seconds=60)       # 1分钟
        
        # 数据库连接池
        self.db_connections = {}
        self.db_lock = threading.Lock()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _get_db_connection(self, db_path: str) -> sqlite3.Connection:
        """获取数据库连接（连接池）"""
        thread_id = threading.get_ident()
        key = f"{db_path}_{thread_id}"
        
        if key not in self.db_connections:
            with self.db_lock:
                if key not in self.db_connections:
                    self.db_connections[key] = sqlite3.connect(db_path)
        
        return self.db_connections[key]
    
    async def get_stock_info(self, stock_code: str, db_path: str = get_database_path("stock_master")) -> Optional[Dict[str, Any]]:
        """获取股票基本信息（带缓存）"""
        try:
            cache_key = f"stock_info_{stock_code}"
            
            # 尝试从缓存获取
            cached_data = self.stock_info_cache.get(cache_key)
            if cached_data:
                return cached_data
            
            # 从数据库获取
            conn = self._get_db_connection(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT stock_code, stock_name, exchange, industry, sector
                FROM stock_info 
                WHERE stock_code = ?
            """, (stock_code,))
            
            row = cursor.fetchone()
            if row:
                stock_info = {
                    "code": row[0],
                    "name": row[1],
                    "exchange": row[2],
                    "industry": row[3],
                    "sector": row[4]
                }
                
                # 缓存结果
                self.stock_info_cache.set(cache_key, stock_info)
                return stock_info
            
            return None
            
        except Exception as e:
            logger.error(f"获取股票信息失败 {stock_code}: {e}")
            return None
    
    async def get_daily_data(self, stock_code: str, days: int = 30, db_path: str = get_database_path("stock_database")) -> List[Dict[str, Any]]:
        """获取股票日线数据（带缓存）"""
        try:
            cache_key = f"daily_data_{stock_code}_{days}"
            
            # 尝试从缓存获取
            cached_data = self.daily_data_cache.get(cache_key)
            if cached_data:
                return cached_data
            
            # 从数据库获取
            conn = self._get_db_connection(db_path)
            cursor = conn.cursor()
            
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            cursor.execute("""
                SELECT trade_date, open_price, close_price, high_price, low_price, 
                       volume, amount, change_percent, turnover_rate
                FROM daily_data 
                WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date DESC
            """, (stock_code, start_date, end_date))
            
            rows = cursor.fetchall()
            daily_data = []
            
            for row in rows:
                daily_data.append({
                    "trade_date": row[0],
                    "open_price": row[1],
                    "close_price": row[2],
                    "high_price": row[3],
                    "low_price": row[4],
                    "volume": row[5],
                    "amount": row[6],
                    "change_percent": row[7],
                    "turnover_rate": row[8]
                })
            
            # 缓存结果
            self.daily_data_cache.set(cache_key, daily_data)
            return daily_data
            
        except Exception as e:
            logger.error(f"获取日线数据失败 {stock_code}: {e}")
            return []
    
    async def get_technical_indicators(self, stock_code: str, days: int = 30, db_path: str = "backend/data/efficient_a_stock_master.db") -> List[Dict[str, Any]]:
        """获取技术指标数据（带缓存）"""
        try:
            cache_key = f"technical_{stock_code}_{days}"
            
            # 尝试从缓存获取
            cached_data = self.technical_cache.get(cache_key)
            if cached_data:
                return cached_data
            
            # 从数据库获取
            conn = self._get_db_connection(db_path)
            cursor = conn.cursor()
            
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            cursor.execute("""
                SELECT trade_date, ma5, ma10, ma20, ma60, rsi, 
                       bollinger_upper, bollinger_lower
                FROM technical_indicators 
                WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
                ORDER BY trade_date DESC
            """, (stock_code, start_date, end_date))
            
            rows = cursor.fetchall()
            tech_data = []
            
            for row in rows:
                tech_data.append({
                    "trade_date": row[0],
                    "ma5": row[1],
                    "ma10": row[2],
                    "ma20": row[3],
                    "ma60": row[4],
                    "rsi": row[5],
                    "bollinger_upper": row[6],
                    "bollinger_lower": row[7]
                })
            
            # 缓存结果
            self.technical_cache.set(cache_key, tech_data)
            return tech_data
            
        except Exception as e:
            logger.error(f"获取技术指标失败 {stock_code}: {e}")
            return []
    
    async def get_batch_stock_info(self, stock_codes: List[str], db_path: str = get_database_path("stock_database")) -> Dict[str, Dict[str, Any]]:
        """批量获取股票信息（带缓存）"""
        try:
            result = {}
            uncached_codes = []
            
            # 检查缓存
            for stock_code in stock_codes:
                cache_key = f"stock_info_{stock_code}"
                cached_data = self.stock_info_cache.get(cache_key)
                if cached_data:
                    result[stock_code] = cached_data
                else:
                    uncached_codes.append(stock_code)
            
            # 批量查询未缓存的数据
            if uncached_codes:
                conn = self._get_db_connection(db_path)
                cursor = conn.cursor()
                
                placeholders = ",".join(["?" for _ in uncached_codes])
                cursor.execute(f"""
                    SELECT stock_code, stock_name, exchange, industry, sector
                    FROM stock_info 
                    WHERE stock_code IN ({placeholders})
                """, uncached_codes)
                
                rows = cursor.fetchall()
                for row in rows:
                    stock_info = {
                        "code": row[0],
                        "name": row[1],
                        "exchange": row[2],
                        "industry": row[3],
                        "sector": row[4]
                    }
                    
                    result[row[0]] = stock_info
                    
                    # 缓存结果
                    cache_key = f"stock_info_{row[0]}"
                    self.stock_info_cache.set(cache_key, stock_info)
            
            return result
            
        except Exception as e:
            logger.error(f"批量获取股票信息失败: {e}")
            return {}
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "stock_info_cache": self.stock_info_cache.get_stats(),
            "daily_data_cache": self.daily_data_cache.get_stats(),
            "technical_cache": self.technical_cache.get_stats(),
            "realtime_cache": self.realtime_cache.get_stats()
        }
    
    def clear_all_caches(self):
        """清空所有缓存"""
        self.stock_info_cache.clear()
        self.daily_data_cache.clear()
        self.technical_cache.clear()
        self.realtime_cache.clear()
        logger.info("✅ 所有缓存已清空")

# 全局缓存服务实例
stock_data_cache_service = StockDataCacheService()
