#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DQN实现
"""

import sys
import os
sys.path.append('.')

def test_pytorch():
    """测试PyTorch"""
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        
        # 测试基本功能
        x = torch.randn(5, 3)
        print(f"✓ 张量创建成功: {x.shape}")
        return True
    except Exception as e:
        print(f"✗ PyTorch测试失败: {e}")
        return False

def test_dqn_import():
    """测试DQN导入"""
    try:
        from roles.tianxuan_star.alternative_data.alternative_data_engine import DQNNetwork, DQNAgent
        print("✓ DQN类导入成功")
        return True
    except Exception as e:
        print(f"✗ DQN导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dqn_creation():
    """测试DQN创建"""
    try:
        from roles.tianxuan_star.alternative_data.alternative_data_engine import DQNAgent
        agent = DQNAgent(state_size=10, action_size=3)
        print(f"✓ DQN智能体创建成功: 状态空间={agent.state_size}, 动作空间={agent.action_size}")
        return True
    except Exception as e:
        print(f"✗ DQN创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dqn_functionality():
    """测试DQN功能"""
    try:
        from roles.tianxuan_star.alternative_data.alternative_data_engine import DQNAgent
        import numpy as np
        
        agent = DQNAgent(state_size=10, action_size=3)
        
        # 测试动作选择
        state = np.random.random(10)
        action = agent.act(state, training=False)
        print(f"✓ 动作选择成功: 动作={action}")
        
        # 测试经验存储
        next_state = np.random.random(10)
        agent.remember(state, action, 0.5, next_state, False)
        print(f"✓ 经验存储成功: 缓冲区大小={len(agent.memory)}")
        
        return True
    except Exception as e:
        print(f"✗ DQN功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== DQN测试开始 ===")
    
    success = True
    success &= test_pytorch()
    success &= test_dqn_import()
    success &= test_dqn_creation()
    success &= test_dqn_functionality()
    
    if success:
        print("=== 所有测试通过 ===")
    else:
        print("=== 部分测试失败 ===")
