<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/瑶光.png" alt="瑶光星" />
          </div>
          <div class="role-details">
            <h1>瑶光星 - 协调任务执行日志</h1>
            <p>六星协调任务监控、执行状态跟踪与历史记录管理</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="status-indicators">
            <div class="indicator" :class="{ online: systemStatus.isOnline }">
              <span class="dot"></span>
              <span>{{ systemStatus.isOnline ? '协调引擎在线' : '系统离线' }}</span>
            </div>
            <div class="indicator">
              <span class="value">{{ taskStats.activeTasks }}</span>
              <span class="label">运行中任务</span>
            </div>
          </div>
          <el-switch
            v-model="autoRefresh"
            @change="toggleAutoRefresh"
            active-text="自动刷新"
            inactive-text="手动刷新"
          />
          <el-button type="primary" @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新任务
          </el-button>
        </div>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.totalTasks }}</div>
          <div class="metric-label">总协调任务</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.tasksTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.completedTasks }}</div>
          <div class="metric-label">已完成任务</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.completedTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.avgExecutionTime }}s</div>
          <div class="metric-label">平均执行时间</div>
          <div class="metric-change negative">
            <el-icon><ArrowDown /></el-icon>
            -{{ taskStats.timeTrend }}%
          </div>
        </div>
      </div>

      <div class="metric-card danger">
        <div class="metric-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ taskStats.failedTasks }}</div>
          <div class="metric-label">失败任务</div>
          <div class="metric-change negative">
            <el-icon><ArrowUp /></el-icon>
            +{{ taskStats.failedTrend }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志监控 -->
    <div class="real-time-monitor">
      <el-card>
        <template #header>
          <div class="monitor-header">
            <h3>实时协调日志监控</h3>
            <div class="monitor-controls">
              <el-select v-model="selectedLogLevel" @change="filterLogs" size="small">
                <el-option label="全部" value="all" />
                <el-option label="信息" value="info" />
                <el-option label="警告" value="warning" />
                <el-option label="错误" value="error" />
              </el-select>
              <el-button @click="clearLogs" size="small">
                <el-icon><Delete /></el-icon>
                清空日志
              </el-button>
              <el-button @click="exportLogs" size="small">
                <el-icon><Download /></el-icon>
                导出日志
              </el-button>
            </div>
          </div>
        </template>
        <div class="log-container" ref="logContainer">
          <div
            v-for="(log, index) in filteredLogs"
            :key="index"
            class="log-entry"
            :class="getLogLevelClass(log.level)"
          >
            <div class="log-timestamp">{{ formatTimestamp(log.timestamp) }}</div>
            <div class="log-level">
              <el-tag :type="getLogLevelType(log.level)" size="small">
                {{ log.level.toUpperCase() }}
              </el-tag>
            </div>
            <div class="log-source">{{ log.source }}</div>
            <div class="log-message">{{ log.message }}</div>
            <div class="log-details" v-if="log.details">
              <el-button @click="toggleLogDetails(index)" size="small" text>
                {{ expandedLogs.includes(index) ? '收起' : '详情' }}
              </el-button>
            </div>
          </div>
          <div v-if="expandedLogs.length > 0" class="log-details-panel">
            <div
              v-for="index in expandedLogs"
              :key="`details-${index}`"
              class="log-detail-content"
            >
              <h4>日志详情 - {{ filteredLogs[index]?.source }}</h4>
              <pre>{{ JSON.stringify(filteredLogs[index]?.details, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务执行历史 -->
    <div class="task-history-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>协调任务执行历史</h3>
            <div class="header-actions">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="loadTaskHistory"
                size="small"
              />
              <el-button @click="exportTaskHistory" size="small">
                <el-icon><Download /></el-icon>
                导出历史
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="taskHistory" v-loading="historyLoading" style="width: 100%">
          <el-table-column prop="task_id" label="任务ID" width="180" />
          <el-table-column prop="task_type" label="任务类型" width="120">
            <template #default="scope">
              <el-tag :type="getTaskTypeColor(scope.row.task_type)" size="small">
                {{ getTaskTypeName(scope.row.task_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="coordination_target" label="协调目标" width="150" />
          <el-table-column prop="execution_time" label="执行时间" width="120">
            <template #default="scope">
              <span :class="getExecutionTimeClass(scope.row.execution_time)">
                {{ scope.row.execution_time }}s
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="agents_involved" label="参与智能体" width="200">
            <template #default="scope">
              <el-tag
                v-for="agent in scope.row.agents_involved"
                :key="agent"
                size="small"
                style="margin-right: 4px;"
              >
                {{ agent }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_time" label="创建时间" width="180" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button @click="viewTaskDetail(scope.row)" size="small">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 智能体状态监控 -->
    <div class="agent-status-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>智能体状态监控</h3>
            <div class="header-actions">
              <el-button @click="refreshAgentStatus" size="small">
                <el-icon><Refresh /></el-icon>
                刷新状态
              </el-button>
            </div>
          </div>
        </template>
        <div class="agent-status-grid">
          <div
            v-for="(agent, key) in agentStatus"
            :key="key"
            class="agent-status-card"
            :class="{ online: agent.is_online, offline: !agent.is_online }"
          >
            <div class="agent-header">
              <div class="agent-name">{{ agent.name }}</div>
              <div class="agent-status">
                <el-tag
                  :type="agent.is_online ? 'success' : 'danger'"
                  size="small"
                >
                  {{ agent.is_online ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
            <div class="agent-metrics">
              <div class="metric-row">
                <span class="metric-label">当前任务:</span>
                <span class="metric-value">{{ agent.current_tasks || 0 }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">完成任务:</span>
                <span class="metric-value">{{ agent.completed_tasks || 0 }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">CPU使用:</span>
                <span class="metric-value">{{ agent.cpu_usage || 0 }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">内存使用:</span>
                <span class="metric-value">{{ agent.memory_usage || 0 }}%</span>
              </div>
            </div>
            <div class="agent-actions">
              <el-button @click="restartAgent(key)" size="small" v-if="!agent.is_online">
                重启
              </el-button>
              <el-button @click="viewAgentLogs(key)" size="small">
                查看日志
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="showTaskDetail" title="任务详情" width="800px">
      <div v-if="selectedTask" class="task-detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.task_id }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ getTaskTypeName(selectedTask.task_type) }}</el-descriptions-item>
          <el-descriptions-item label="协调目标">{{ selectedTask.coordination_target }}</el-descriptions-item>
          <el-descriptions-item label="执行时间">{{ selectedTask.execution_time }}s</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)" size="small">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedTask.created_time }}</el-descriptions-item>
        </el-descriptions>

        <div class="task-agents-section">
          <h4>参与智能体</h4>
          <div class="agents-list">
            <el-tag
              v-for="agent in selectedTask.agents_involved"
              :key="agent"
              size="small"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ agent }}
            </el-tag>
          </div>
        </div>

        <div class="task-logs-section" v-if="selectedTask.logs">
          <h4>任务日志</h4>
          <div class="task-logs">
            <div
              v-for="(log, index) in selectedTask.logs"
              :key="index"
              class="task-log-entry"
            >
              <span class="log-time">{{ formatTimestamp(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>

        <div class="task-result-section" v-if="selectedTask.result">
          <h4>执行结果</h4>
          <pre class="task-result">{{ JSON.stringify(selectedTask.result, null, 2) }}</pre>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTaskDetail = false">关闭</el-button>
        <el-button type="primary" @click="retryTask" v-if="selectedTask?.status === 'failed'">
          重试任务
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  ArrowUp,
  ArrowDown,
  Connection,
  CircleCheck,
  Clock,
  Warning,
  Delete,
  Download
} from '@element-plus/icons-vue'
import { YaoguangStarAPI } from '@/api/roles/yaoguang-star'

// 响应式数据
const loading = ref(false)
const historyLoading = ref(false)
const autoRefresh = ref(false)
const selectedLogLevel = ref('all')
const dateRange = ref([])
const showTaskDetail = ref(false)
const selectedTask = ref(null)
const expandedLogs = ref([])

const systemStatus = ref({
  isOnline: true
})

const taskStats = ref({
  totalTasks: 0,
  completedTasks: 0,
  activeTasks: 0,
  failedTasks: 0,
  avgExecutionTime: 0,
  tasksTrend: 0,      // 从API获取，不再硬编码
  completedTrend: 0,  // 从API获取，不再硬编码
  timeTrend: 0,       // 从API获取，不再硬编码
  failedTrend: 0      // 从API获取，不再硬编码
})

const realTimeLogs = ref([])
const filteredLogs = ref([])
const taskHistory = ref([])
const agentStatus = ref({})

let refreshInterval: number | null = null
const logContainer = ref()

// 方法
const loadTaskLogs = async () => {
  try {
    loading.value = true
    const response = await YaoguangStarAPI.getTaskLogs()

    if (response.data) {
      const data = response.data

      // 更新任务统计
      taskStats.value = {
        ...taskStats.value,
        ...data.task_stats
      }

      // 更新实时日志
      realTimeLogs.value = data.real_time_logs || []
      filterLogs()
    }
  } catch (error) {
    console.error('加载任务日志失败:', error)
    ElMessage.error('加载任务日志失败')
  } finally {
    loading.value = false
  }
}

const loadTaskHistory = async () => {
  try {
    historyLoading.value = true
    const params: any = {}

    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }

    const response = await YaoguangStarAPI.getTaskLogs(params)

    if (response.data) {
      taskHistory.value = response.data.task_history || []
    }
  } catch (error) {
    console.error('加载任务历史失败:', error)
    ElMessage.error('加载任务历史失败')
  } finally {
    historyLoading.value = false
  }
}

const loadAgentStatus = async () => {
  try {
    const response = await YaoguangStarAPI.getSixStarsCoordinationStatus()

    if (response.data) {
      agentStatus.value = response.data.stars_status || {}
    }
  } catch (error) {
    console.error('加载智能体状态失败:', error)
    ElMessage.error('加载智能体状态失败')
  }
}

const refreshTasks = async () => {
  await loadTaskLogs()
  await loadTaskHistory()
  await loadAgentStatus()
  ElMessage.success('任务数据刷新成功')
}

const refreshAgentStatus = async () => {
  await loadAgentStatus()
  ElMessage.success('智能体状态刷新成功')
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshInterval = setInterval(() => {
      loadTaskLogs()
    }, 5000) // 每5秒刷新一次
    ElMessage.success('自动刷新已启用')

    // 注释掉WebSocket连接，使用轮询代替
    // connectWebSocket()
  } else {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
    }
    ElMessage.info('自动刷新已关闭')

    // 注释掉WebSocket断开
    // disconnectWebSocket()
  }
}

const filterLogs = () => {
  if (selectedLogLevel.value === 'all') {
    filteredLogs.value = realTimeLogs.value
  } else {
    filteredLogs.value = realTimeLogs.value.filter(log => log.level === selectedLogLevel.value)
  }

  // 滚动到底部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

const clearLogs = () => {
  realTimeLogs.value = []
  filteredLogs.value = []
  expandedLogs.value = []
  ElMessage.success('日志已清空')
}

const exportLogs = () => {
  ElMessage.success('日志导出成功')
}

const exportTaskHistory = () => {
  ElMessage.success('任务历史导出成功')
}

const toggleLogDetails = (index: number) => {
  const expandedIndex = expandedLogs.value.indexOf(index)
  if (expandedIndex > -1) {
    expandedLogs.value.splice(expandedIndex, 1)
  } else {
    expandedLogs.value.push(index)
  }
}

const viewTaskDetail = (task: any) => {
  selectedTask.value = task
  showTaskDetail.value = true
}

const restartAgent = (agentKey: string) => {
  ElMessage.success(`正在重启 ${agentKey}`)
}

const viewAgentLogs = (agentKey: string) => {
  ElMessage.info(`查看 ${agentKey} 的日志`)
}

const retryTask = () => {
  if (selectedTask.value) {
    ElMessage.success(`正在重试任务: ${selectedTask.value.task_id}`)
    showTaskDetail.value = false
  }
}

// 辅助方法
const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const getLogLevelClass = (level: string) => {
  switch (level) {
    case 'error': return 'log-error'
    case 'warning': return 'log-warning'
    case 'info': return 'log-info'
    default: return 'log-debug'
  }
}

const getLogLevelType = (level: string) => {
  switch (level) {
    case 'error': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'primary'
    default: return 'info'
  }
}

const getTaskTypeColor = (type: string) => {
  switch (type) {
    case 'learning': return 'success'
    case 'backtest': return 'warning'
    case 'coordination': return 'primary'
    default: return 'info'
  }
}

const getTaskTypeName = (type: string) => {
  switch (type) {
    case 'learning': return '学习协调'
    case 'backtest': return '回测协调'
    case 'coordination': return '智能体协调'
    default: return '未知类型'
  }
}

const getExecutionTimeClass = (time: number) => {
  if (time <= 5) return 'text-green-500'
  if (time <= 15) return 'text-yellow-500'
  return 'text-red-500'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'running': return 'primary'
    case 'failed': return 'danger'
    case 'pending': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'running': return '运行中'
    case 'failed': return '失败'
    case 'pending': return '等待中'
    default: return '未知'
  }
}

onMounted(() => {
  loadTaskLogs()
  loadTaskHistory()
  loadAgentStatus()
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 瑶光星任务日志特定样式 */
.role-page-container {
  background: linear-gradient(135deg, #f5f0ff 0%, #e6e0ff 100%);
}

.page-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-details h1 {
  color: white;
}

.role-details p {
  color: rgba(255, 255, 255, 0.9);
}

.status-indicators {
  display: flex;
  gap: 16px;
  align-items: center;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 14px;
}

.indicator.online .dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator .value {
  font-weight: 600;
  font-size: 16px;
}

.indicator .label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 实时日志监控 */
.real-time-monitor {
  margin-bottom: 24px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.monitor-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  background: #1a1a1a;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
}

.log-entry {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #333;
  font-size: 12px;
}

.log-entry.log-error {
  color: #f56c6c;
}

.log-entry.log-warning {
  color: #e6a23c;
}

.log-entry.log-info {
  color: #409eff;
}

.log-entry.log-debug {
  color: #909399;
}

.log-timestamp {
  min-width: 120px;
  color: #909399;
}

.log-level {
  min-width: 60px;
}

.log-source {
  min-width: 80px;
  font-weight: 600;
}

.log-message {
  flex: 1;
}

.log-details-panel {
  margin-top: 16px;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 4px;
}

.log-detail-content {
  margin-bottom: 16px;
}

.log-detail-content h4 {
  margin: 0 0 8px 0;
  color: #409eff;
}

.log-detail-content pre {
  background: #1a1a1a;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 11px;
  color: #e6e6e6;
}

/* 任务历史 */
.task-history-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.text-green-500 {
  color: #10b981;
}

.text-yellow-500 {
  color: #f59e0b;
}

.text-red-500 {
  color: #ef4444;
}

/* 智能体状态监控 */
.agent-status-section {
  margin-bottom: 24px;
}

.agent-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.agent-status-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
  transition: all 0.3s ease;
}

.agent-status-card.online {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.agent-status-card.offline {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.agent-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.agent-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
}

.agent-actions {
  display: flex;
  gap: 8px;
}

/* 任务详情对话框 */
.task-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.task-agents-section,
.task-logs-section,
.task-result-section {
  margin-top: 20px;
}

.task-agents-section h4,
.task-logs-section h4,
.task-result-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.agents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.task-logs {
  max-height: 200px;
  overflow-y: auto;
  background: #f9fafb;
  border-radius: 4px;
  padding: 8px;
}

.task-log-entry {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
}

.log-time {
  min-width: 120px;
  color: #6b7280;
}

.task-result {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 8px;
  }

  .monitor-header,
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .monitor-controls,
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .agent-status-grid {
    grid-template-columns: 1fr;
  }

  .log-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>