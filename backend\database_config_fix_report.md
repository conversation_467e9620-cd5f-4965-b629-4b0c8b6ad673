# 数据库配置修复报告

## 🔍 问题诊断

在启动系统时，发现以下错误：

```
ValueError: 未知的数据库名称: stock_database
```

### 根本原因分析

1. 系统中多处代码引用了 `stock_database`，但在 `database_config.py` 中没有定义这个数据库名称
2. 根据用户要求，系统应该使用三个标准数据库：
   - `stock_master.db` - 主数据库（股票基本信息）
   - `stock_realtime.db` - 实时数据库（实时价格数据）
   - `stock_historical.db` - 历史数据库（15GB历史数据）
3. 代码中的 `stock_database` 引用应该根据上下文替换为正确的数据库名称

## 🛠️ 修复措施

### 1. 添加向后兼容性别名

在 `backend/config/database_config.py` 中添加了 `stock_database` 作为 `stock_master` 的别名：

```python
# 向后兼容性别名
"stock_database": DATABASE_BASE_PATH / "stock_master.db",  # 别名：指向主数据库
```

### 2. 修复特定文件中的引用

修复了以下文件中的 `stock_database` 引用：

1. **backend/main.py**
   - 第361行：`self.performance_db_path = get_database_path("stock_database")` → `self.performance_db_path = get_database_path("stock_master")`
   - 第519行：`db_path = get_database_path("stock_database")` → `db_path = get_database_path("stock_master")`
   - 第605行：`db_path = get_database_path("stock_database")` → `db_path = get_database_path("stock_realtime")`

2. **backend/shared/database/unified_database_path_manager.py**
   - 第35行：`"stock_database": self.data_root / "stock_historical.db"` → `"stock_database": self.data_root / "stock_master.db"`

3. **backend/roles/kaiyang_star/utils/database_manager.py**
   - 第50行：`with self.get_connection("stock_database") as conn:` → `with self.get_connection("stock_master") as conn:`

4. **backend/roles/tianji_star/workflows/portfolio_analysis_workflow.py**
   - 第244行：`conn = sqlite3.connect(get_database_path("stock_database"))` → `conn = sqlite3.connect(get_database_path("stock_historical"))`

5. **backend/roles/tianji_star/workflows/stress_test_workflow.py**
   - 第229行：`conn = sqlite3.connect(get_database_path("stock_database"))` → `conn = sqlite3.connect(get_database_path("stock_historical"))`

6. **backend/services/cache/stock_data_cache.py**
   - 第153行：`async def get_stock_info(self, stock_code: str, db_path: str = get_database_path("stock_database"))` → `async def get_stock_info(self, stock_code: str, db_path: str = get_database_path("stock_master"))`

7. **backend/scripts/rebuild_stock_database.py**
   - 第44行：`self.stock_db_path = self.path_manager.get_database_path("stock_database")` → `self.stock_db_path = self.path_manager.get_database_path("stock_master")`

8. **backend/config/data_source_config.json**
   - 第3行：`"source": "stock_database",` → `"source": "stock_master",`

9. **backend/roles/tianquan_star/config/complete_architecture_understanding.py**
   - 第100行：`"stock_database": "股票基础数据",` → `"stock_master": "股票基础数据",`

## 📊 修复策略

### 数据库映射规则

根据上下文，我们采用以下映射规则：

1. 当代码需要**股票基本信息**时，使用 `stock_master`
2. 当代码需要**实时价格数据**时，使用 `stock_realtime`
3. 当代码需要**历史价格数据**时，使用 `stock_historical`

### 向后兼容性考虑

为了确保系统稳定性和向后兼容性，我们：

1. 添加了 `stock_database` 作为 `stock_master` 的别名
2. 保留了原有的数据库路径结构
3. 确保所有数据库引用都指向正确的数据库文件

## ✅ 验证步骤

修复后，系统应该能够正常启动，不再出现 `未知的数据库名称: stock_database` 错误。

建议进行以下验证：

1. 启动后端服务，确认没有数据库错误
2. 检查性能服务是否能正常访问数据库
3. 验证股票列表获取功能是否正常
4. 验证实时价格数据获取功能是否正常

## 🔄 后续建议

为了进一步提高系统稳定性和可维护性，建议：

1. 统一数据库访问接口，避免直接使用 `sqlite3.connect`
2. 使用依赖注入方式提供数据库连接，而不是硬编码数据库路径
3. 添加数据库连接池，提高性能和稳定性
4. 增加数据库访问日志，便于问题排查
5. 考虑使用 ORM 框架，提高代码可读性和可维护性
