"""
第二阶段：可执行建议生成服务
基于实时数据生成可直接执行的投资建议
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
import json
from datetime import datetime
import numpy as np

from services.intent_recognition_service import ChatIntent
# 修复导入路径
try:
    from services.realtime_data_service import (
        realtime_data_service, MarketData, UserPortfolio, TechnicalIndicators
    )
except ImportError:
    # 如果导入失败，创建空的类定义
    class MarketData:
        pass
    class UserPortfolio:
        pass
    class TechnicalIndicators:
        pass
    realtime_data_service = None

class ActionType(Enum):
    """操作类型枚举"""
    BUY_ORDER = "buy_order"
    SELL_ORDER = "sell_order"
    SET_ALERT = "set_alert"
    CREATE_STRATEGY = "create_strategy"
    ADJUST_POSITION = "adjust_position"
    SET_STOP_LOSS = "set_stop_loss"
    SET_TAKE_PROFIT = "set_take_profit"
    REBALANCE_PORTFOLIO = "rebalance_portfolio"
    RISK_WARNING = "risk_warning"
    MARKET_WATCH = "market_watch"

@dataclass
class ExecutableAction:
    """可执行的操作"""
    action_id: str
    type: ActionType
    title: str
    description: str
    parameters: Dict[str, Any]
    confidence: float
    risk_level: str  # low, medium, high
    estimated_impact: str
    expected_return: Optional[float]
    max_risk: Optional[float]
    time_horizon: str  # short, medium, long
    prerequisites: List[str]
    execution_steps: List[str]

@dataclass
class ChatContext:
    """聊天上下文"""
    user_id: str
    session_id: str
    portfolio_data: Optional[Dict] = None
    market_context: Optional[Dict] = None
    recent_trades: Optional[List] = None
    risk_profile: Optional[Dict] = None

@dataclass
class EnhancedChatResponse:
    """增强的聊天响应"""
    text_response: str
    role_responses: List[Dict]
    data_visualizations: List[Dict]
    executable_actions: List[ExecutableAction]
    risk_warnings: List[str]
    follow_up_suggestions: List[str]
    confidence_score: float
    processing_time: float

class QuantChatIntentAnalyzer:
    """量化交易聊天意图分析器"""
    
    def __init__(self):
        self.intent_keywords = {
            ChatIntent.MARKET_ANALYSIS: [
                "市场", "行情", "趋势", "大盘", "指数", "走势", "分析", "预测"
            ],
            ChatIntent.STOCK_SELECTION: [
                "选股", "推荐", "筛选", "股票", "买什么", "投资标的", "机会"
            ],
            ChatIntent.RISK_ASSESSMENT: [
                "风险", "回撤", "止损", "风控", "安全", "评估", "波动"
            ],
            ChatIntent.STRATEGY_CREATION: [
                "策略", "交易", "买入", "卖出", "操作", "计划", "方案"
            ],
            ChatIntent.PORTFOLIO_MANAGEMENT: [
                "组合", "配置", "仓位", "资产", "分配", "调整", "优化"
            ],
            ChatIntent.PERFORMANCE_REVIEW: [
                "收益", "绩效", "表现", "回测", "业绩", "盈亏", "统计"
            ],
            ChatIntent.TECHNICAL_ANALYSIS: [
                "技术", "指标", "K线", "均线", "MACD", "RSI", "图表"
            ],
            ChatIntent.NEWS_ANALYSIS: [
                "新闻", "消息", "公告", "事件", "政策", "影响", "解读"
            ]
        }
    
    def analyze_intent(self, message: str, context: ChatContext) -> ChatIntent:
        """分析用户意图"""
        message_lower = message.lower()
        
        # 基于关键词匹配
        intent_scores = {}
        for intent, keywords in self.intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            return max(intent_scores.items(), key=lambda x: x[1])[0]
        
        return ChatIntent.GENERAL_QUERY
    
    def extract_entities(self, message: str) -> Dict[str, Any]:
        """提取实体信息"""
        entities = {
            "stocks": [],
            "timeframe": None,
            "amount": None,
            "indicators": [],
            "sectors": []
        }
        
        # 股票代码提取
        import re
        stock_pattern = r'\b\d{6}\b|\b[A-Z]{2,4}\b'
        entities["stocks"] = re.findall(stock_pattern, message)
        
        # 时间框架提取
        if any(word in message for word in ["今天", "今日", "当日"]):
            entities["timeframe"] = "1d"
        elif any(word in message for word in ["本周", "这周"]):
            entities["timeframe"] = "1w"
        elif any(word in message for word in ["本月", "这月"]):
            entities["timeframe"] = "1m"
        
        return entities

class EnhancedQuantChatService:
    """增强的量化交易聊天服务"""
    
    def __init__(self):
        self.intent_analyzer = QuantChatIntentAnalyzer()
        self.role_specialists = {
            ChatIntent.MARKET_ANALYSIS: "tianshu",
            ChatIntent.TECHNICAL_ANALYSIS: "tianxuan", 
            ChatIntent.RISK_ASSESSMENT: "tianji",
            ChatIntent.STRATEGY_CREATION: "tianquan",
            ChatIntent.PORTFOLIO_MANAGEMENT: "yuheng",
            ChatIntent.STOCK_SELECTION: "kaiyang",
            ChatIntent.PERFORMANCE_REVIEW: "yaoguang"
        }
    
    async def process_chat(self, message: str, context: ChatContext) -> EnhancedChatResponse:
        """处理聊天请求"""
        start_time = datetime.now()
        
        # 1. 意图分析
        intent = self.intent_analyzer.analyze_intent(message, context)
        entities = self.intent_analyzer.extract_entities(message)
        
        # 2. 获取相关数据
        relevant_data = await self.fetch_relevant_data(intent, entities, context)
        
        # 3. 选择专业角色
        primary_role = self.role_specialists.get(intent, "tianquan")
        
        # 4. 生成专业响应
        role_responses = await self.generate_role_responses(
            intent, message, entities, relevant_data, primary_role
        )
        
        # 5. 生成可执行建议
        executable_actions = await self.generate_executable_actions(
            intent, entities, relevant_data, role_responses
        )
        
        # 6. 风险评估
        risk_warnings = await self.assess_risks(executable_actions, context)
        
        # 7. 生成数据可视化
        visualizations = await self.create_visualizations(intent, relevant_data)
        
        # 8. 后续建议
        follow_ups = self.generate_follow_up_suggestions(intent, entities)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return EnhancedChatResponse(
            text_response=self.synthesize_text_response(role_responses),
            role_responses=role_responses,
            data_visualizations=visualizations,
            executable_actions=executable_actions,
            risk_warnings=risk_warnings,
            follow_up_suggestions=follow_ups,
            confidence_score=self.calculate_confidence(role_responses),
            processing_time=processing_time
        )
    
    async def fetch_relevant_data(self, intent: ChatIntent, entities: Dict, context: ChatContext) -> Dict:
        """获取相关数据"""
        data = {}
        
        if intent == ChatIntent.MARKET_ANALYSIS:
            # 获取市场数据
            data["market_indices"] = await self.get_market_indices()
            data["sector_performance"] = await self.get_sector_performance()
            data["market_sentiment"] = await self.get_market_sentiment()
        
        elif intent == ChatIntent.STOCK_SELECTION:
            # 获取股票筛选数据
            data["stock_screener"] = await self.run_stock_screener()
            data["top_performers"] = await self.get_top_performers()
            data["analyst_ratings"] = await self.get_analyst_ratings()
        
        elif intent == ChatIntent.RISK_ASSESSMENT:
            # 获取风险数据
            data["portfolio_risk"] = await self.calculate_portfolio_risk(context)
            data["market_volatility"] = await self.get_market_volatility()
            data["correlation_matrix"] = await self.get_correlation_matrix()
        
        return data
    
    async def generate_executable_actions(self, intent: ChatIntent, entities: Dict, 
                                        data: Dict, responses: List[Dict]) -> List[ExecutableAction]:
        """生成可执行的操作建议"""
        actions = []
        
        if intent == ChatIntent.STOCK_SELECTION and "stocks" in entities:
            for stock in entities["stocks"]:
                action = ExecutableAction(
                    type="buy_order",
                    parameters={
                        "symbol": stock,
                        "quantity": 100,
                        "order_type": "market"
                    },
                    confidence=0.75,
                    risk_level="medium",
                    estimated_impact="预计影响组合收益2-3%"
                )
                actions.append(action)
        
        elif intent == ChatIntent.RISK_ASSESSMENT:
            action = ExecutableAction(
                type="set_stop_loss",
                parameters={
                    "percentage": 5.0,
                    "apply_to": "all_positions"
                },
                confidence=0.90,
                risk_level="low",
                estimated_impact="限制最大损失5%"
            )
            actions.append(action)
        
        return actions
    
    def generate_follow_up_suggestions(self, intent: ChatIntent, entities: Dict) -> List[str]:
        """生成后续建议"""
        suggestions = []
        
        if intent == ChatIntent.MARKET_ANALYSIS:
            suggestions = [
                "需要我分析具体板块的机会吗？",
                "要不要看看技术指标的信号？",
                "是否需要风险评估？"
            ]
        elif intent == ChatIntent.STOCK_SELECTION:
            suggestions = [
                "需要我分析这些股票的风险吗？",
                "要设置买入提醒吗？",
                "需要制定具体的交易策略吗？"
            ]
        
        return suggestions
    
    async def get_market_indices(self) -> Dict:
        """获取市场指数数据"""
        # 这里应该调用实际的数据服务
        return {
            "shanghai_composite": {"value": 3200, "change": 1.2},
            "shenzhen_component": {"value": 11500, "change": 0.8},
            "csi_300": {"value": 4100, "change": 1.0}
        }
    
    async def run_stock_screener(self) -> List[Dict]:
        """运行股票筛选器"""
        # 这里应该调用实际的筛选服务
        return [
            {"symbol": "000001", "name": "平安银行", "score": 85},
            {"symbol": "000002", "name": "万科A", "score": 78}
        ]
    
    def synthesize_text_response(self, role_responses: List[Dict]) -> str:
        """合成文本响应"""
        if not role_responses:
            return "抱歉，暂时无法处理您的请求。"
        
        # 合成多个角色的响应
        main_response = role_responses[0].get("content", "")
        return main_response
    
    def calculate_confidence(self, role_responses: List[Dict]) -> float:
        """计算置信度"""
        if not role_responses:
            return 0.0
        
        # 基于响应质量计算置信度
        return 0.85  # 示例值
