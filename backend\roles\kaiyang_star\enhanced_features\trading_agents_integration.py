#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星TradingAgents框架集成
基于TradingAgents框架增强开阳星的分析和决策能力
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class TradingAgentsIntegration:
    """TradingAgents框架集成服务"""
    
    def __init__(self):
        self.service_name = "TradingAgents集成服务"
        self.version = "1.0.0"
        
        # 分析师角色配置
        self.analyst_roles = {
            "bull_analyst": {
                "name": "多头分析师",
                "perspective": "积极看多",
                "focus": ["成长潜力", "竞争优势", "正面指标"],
                "prompt_template": self._get_bull_prompt_template()
            },
            "bear_analyst": {
                "name": "空头分析师", 
                "perspective": "谨慎看空",
                "focus": ["风险挑战", "竞争劣势", "负面指标"],
                "prompt_template": self._get_bear_prompt_template()
            },
            "neutral_analyst": {
                "name": "中性分析师",
                "perspective": "客观中性",
                "focus": ["平衡分析", "风险收益", "理性评估"],
                "prompt_template": self._get_neutral_prompt_template()
            }
        }
        
        # 决策管理器配置
        self.decision_manager_config = {
            "name": "投资决策管理器",
            "role": "综合评估和最终决策",
            "decision_criteria": ["论据强度", "数据支撑", "风险评估", "收益预期"],
            "prompt_template": self._get_decision_manager_prompt_template()
        }
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    async def conduct_investment_debate(self, stock_data: Dict[str, Any], 
                                      market_context: Dict[str, Any]) -> Dict[str, Any]:
        """进行投资辩论分析"""
        try:
            logger.info(f"🎯 开始投资辩论分析: {stock_data.get('name', 'unknown')}")
            
            # 准备分析数据
            analysis_context = self._prepare_analysis_context(stock_data, market_context)
            
            # 多头分析
            bull_analysis = await self._conduct_bull_analysis(analysis_context)
            
            # 空头分析  
            bear_analysis = await self._conduct_bear_analysis(analysis_context)
            
            # 中性分析
            neutral_analysis = await self._conduct_neutral_analysis(analysis_context)
            
            # 决策管理
            final_decision = await self._make_investment_decision(
                bull_analysis, bear_analysis, neutral_analysis, analysis_context
            )
            
            # 生成投资计划
            investment_plan = await self._generate_investment_plan(final_decision, analysis_context)
            
            result = {
                "stock_code": stock_data.get("code"),
                "stock_name": stock_data.get("name"),
                "analysis_timestamp": datetime.now().isoformat(),
                "debate_results": {
                    "bull_analysis": bull_analysis,
                    "bear_analysis": bear_analysis,
                    "neutral_analysis": neutral_analysis
                },
                "final_decision": final_decision,
                "investment_plan": investment_plan,
                "confidence_score": final_decision.get("confidence", 0.5),
                "recommendation": final_decision.get("recommendation", "HOLD")
            }
            
            logger.info(f"✅ 投资辩论完成: {final_decision.get('recommendation', 'HOLD')}")
            return result
            
        except Exception as e:
            logger.error(f"投资辩论分析失败: {e}")
            return {"error": str(e)}
    
    def _prepare_analysis_context(self, stock_data: Dict[str, Any], 
                                market_context: Dict[str, Any]) -> Dict[str, Any]:
        """准备分析上下文"""
        return {
            "stock_info": {
                "code": stock_data.get("code"),
                "name": stock_data.get("name"),
                "current_price": stock_data.get("current_price", 0),
                "pe_ratio": stock_data.get("pe_ratio", 0),
                "pb_ratio": stock_data.get("pb_ratio", 0),
                "market_cap": stock_data.get("market_cap", 0),
                "industry": stock_data.get("industry", ""),
                "detailed_scores": stock_data.get("detailed_scores", {})
            },
            "market_context": {
                "market_sentiment": market_context.get("overall_sentiment", "中性"),
                "hot_sectors": market_context.get("hot_sectors", []),
                "volatility": market_context.get("volatility", 0.2),
                "news_volume": market_context.get("news_volume", 0)
            },
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    async def _conduct_bull_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进行多头分析"""
        try:
            stock_info = context["stock_info"]
            market_context = context["market_context"]
            
            # 多头观点分析
            bull_points = []
            
            # 基本面优势
            if stock_info.get("detailed_scores", {}).get("fundamental_score", 0) > 70:
                bull_points.append("基本面强劲，ROE和盈利增长表现优异")
            
            # 估值优势
            if stock_info.get("pe_ratio", 0) > 0 and stock_info.get("pe_ratio", 0) < 20:
                bull_points.append(f"估值合理，PE比率{stock_info.get('pe_ratio'):.1f}具有吸引力")
            
            # 成长性优势
            if stock_info.get("detailed_scores", {}).get("growth_score", 0) > 75:
                bull_points.append("成长性突出，营收和利润增长前景良好")
            
            # 市场环境优势
            if market_context.get("market_sentiment") == "积极":
                bull_points.append("市场情绪积极，有利于股价表现")
            
            # 行业地位优势
            if stock_info.get("detailed_scores", {}).get("industry_position_score", 0) > 80:
                bull_points.append("行业地位领先，具有竞争优势")
            
            analysis = {
                "perspective": "多头看多",
                "key_points": bull_points,
                "strength_score": len(bull_points) * 20,  # 每个优势20分
                "recommendation_tendency": "BUY" if len(bull_points) >= 3 else "HOLD",
                "confidence": min(0.9, len(bull_points) * 0.2),
                "detailed_reasoning": self._generate_bull_reasoning(bull_points, stock_info)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"多头分析失败: {e}")
            return {"error": str(e)}
    
    async def _conduct_bear_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进行空头分析"""
        try:
            stock_info = context["stock_info"]
            market_context = context["market_context"]
            
            # 空头观点分析
            bear_points = []
            
            # 估值风险
            if stock_info.get("pe_ratio", 0) > 30:
                bear_points.append(f"估值偏高，PE比率{stock_info.get('pe_ratio'):.1f}存在泡沫风险")
            
            # 财务健康风险
            if stock_info.get("detailed_scores", {}).get("financial_health_score", 0) < 60:
                bear_points.append("财务健康状况堪忧，负债率或现金流存在问题")
            
            # 技术面风险
            if stock_info.get("detailed_scores", {}).get("technical_score", 0) < 40:
                bear_points.append("技术面疲弱，趋势和动量指标不佳")
            
            # 市场环境风险
            if market_context.get("market_sentiment") == "消极":
                bear_points.append("市场情绪悲观，不利于股价表现")
            
            # 波动率风险
            if market_context.get("volatility", 0) > 0.3:
                bear_points.append("市场波动率较高，投资风险增大")
            
            analysis = {
                "perspective": "空头看空",
                "key_points": bear_points,
                "risk_score": len(bear_points) * 20,  # 每个风险20分
                "recommendation_tendency": "SELL" if len(bear_points) >= 3 else "HOLD",
                "confidence": min(0.9, len(bear_points) * 0.2),
                "detailed_reasoning": self._generate_bear_reasoning(bear_points, stock_info)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"空头分析失败: {e}")
            return {"error": str(e)}
    
    async def _conduct_neutral_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进行中性分析"""
        try:
            stock_info = context["stock_info"]
            
            # 中性观点分析
            neutral_points = []
            
            # 平衡评估
            fundamental_score = stock_info.get("detailed_scores", {}).get("fundamental_score", 50)
            valuation_score = stock_info.get("detailed_scores", {}).get("valuation_score", 50)
            
            if 40 <= fundamental_score <= 80:
                neutral_points.append("基本面表现中等，无明显优劣势")
            
            if 30 <= valuation_score <= 70:
                neutral_points.append("估值水平合理，无明显高估或低估")
            
            # 风险收益平衡
            neutral_points.append("风险与收益基本平衡，适合稳健投资者")
            
            analysis = {
                "perspective": "中性客观",
                "key_points": neutral_points,
                "balance_score": (fundamental_score + valuation_score) / 2,
                "recommendation_tendency": "HOLD",
                "confidence": 0.6,
                "detailed_reasoning": self._generate_neutral_reasoning(neutral_points, stock_info)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"中性分析失败: {e}")
            return {"error": str(e)}
    
    async def _make_investment_decision(self, bull_analysis: Dict[str, Any],
                                      bear_analysis: Dict[str, Any],
                                      neutral_analysis: Dict[str, Any],
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """做出投资决策"""
        try:
            # 计算各方观点权重
            bull_strength = bull_analysis.get("strength_score", 0)
            bear_risk = bear_analysis.get("risk_score", 0)
            neutral_balance = neutral_analysis.get("balance_score", 50)
            
            # 综合评分
            total_score = bull_strength - bear_risk + neutral_balance
            
            # 决策逻辑
            if total_score > 80 and bull_strength > bear_risk:
                recommendation = "BUY"
                confidence = min(0.9, bull_analysis.get("confidence", 0.5) + 0.2)
            elif total_score < 20 or bear_risk > bull_strength + 40:
                recommendation = "SELL"
                confidence = min(0.9, bear_analysis.get("confidence", 0.5) + 0.2)
            else:
                recommendation = "HOLD"
                confidence = neutral_analysis.get("confidence", 0.6)
            
            decision = {
                "recommendation": recommendation,
                "confidence": confidence,
                "total_score": total_score,
                "bull_strength": bull_strength,
                "bear_risk": bear_risk,
                "neutral_balance": neutral_balance,
                "decision_reasoning": self._generate_decision_reasoning(
                    recommendation, bull_analysis, bear_analysis, neutral_analysis
                ),
                "timestamp": datetime.now().isoformat()
            }
            
            return decision
            
        except Exception as e:
            logger.error(f"投资决策失败: {e}")
            return {"error": str(e)}
    
    async def _generate_investment_plan(self, decision: Dict[str, Any], 
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """生成投资计划"""
        try:
            recommendation = decision.get("recommendation", "HOLD")
            confidence = decision.get("confidence", 0.5)
            
            plan = {
                "action": recommendation,
                "confidence_level": confidence,
                "position_size": self._calculate_position_size(recommendation, confidence),
                "entry_strategy": self._generate_entry_strategy(recommendation, context),
                "risk_management": self._generate_risk_management(recommendation, context),
                "exit_strategy": self._generate_exit_strategy(recommendation, context),
                "monitoring_points": self._generate_monitoring_points(context),
                "timeline": self._generate_timeline(recommendation)
            }
            
            return plan
            
        except Exception as e:
            logger.error(f"生成投资计划失败: {e}")
            return {"error": str(e)}
    
    def _calculate_position_size(self, recommendation: str, confidence: float) -> str:
        """计算仓位大小"""
        if recommendation == "BUY":
            if confidence > 0.8:
                return "大仓位 (5-8%)"
            elif confidence > 0.6:
                return "中等仓位 (3-5%)"
            else:
                return "小仓位 (1-3%)"
        elif recommendation == "SELL":
            return "减仓或清仓"
        else:
            return "维持现有仓位"
    
    def _generate_entry_strategy(self, recommendation: str, context: Dict[str, Any]) -> str:
        """生成入场策略"""
        if recommendation == "BUY":
            return "分批建仓，关注技术支撑位入场"
        elif recommendation == "SELL":
            return "逐步减仓，避免集中抛售"
        else:
            return "观望等待，寻找更好时机"
    
    def _generate_risk_management(self, recommendation: str, context: Dict[str, Any]) -> str:
        """生成风险管理策略"""
        return "设置止损位，控制单笔损失不超过2%，密切关注市场变化"
    
    def _generate_exit_strategy(self, recommendation: str, context: Dict[str, Any]) -> str:
        """生成退出策略"""
        if recommendation == "BUY":
            return "设置止盈目标，分批获利了结"
        else:
            return "根据市场情况灵活调整"
    
    def _generate_monitoring_points(self, context: Dict[str, Any]) -> List[str]:
        """生成监控要点"""
        return [
            "关注公司基本面变化",
            "监控行业政策动向", 
            "跟踪技术指标变化",
            "观察市场情绪波动"
        ]
    
    def _generate_timeline(self, recommendation: str) -> str:
        """生成时间规划"""
        if recommendation == "BUY":
            return "短期1-3个月，中期3-6个月"
        elif recommendation == "SELL":
            return "尽快执行，1-2周内完成"
        else:
            return "持续观察，1个月内重新评估"

# 辅助方法
    def _generate_bull_reasoning(self, points: List[str], stock_info: Dict[str, Any]) -> str:
        """生成多头推理"""
        return f"基于{stock_info.get('name', 'unknown')}的分析，" + "；".join(points)
    
    def _generate_bear_reasoning(self, points: List[str], stock_info: Dict[str, Any]) -> str:
        """生成空头推理"""
        return f"对于{stock_info.get('name', 'unknown')}存在担忧，" + "；".join(points)
    
    def _generate_neutral_reasoning(self, points: List[str], stock_info: Dict[str, Any]) -> str:
        """生成中性推理"""
        return f"客观评估{stock_info.get('name', 'unknown')}，" + "；".join(points)
    
    def _generate_decision_reasoning(self, recommendation: str, bull: Dict, bear: Dict, neutral: Dict) -> str:
        """生成决策推理"""
        return f"综合多空双方观点，最终建议{recommendation}，主要基于{bull.get('key_points', [])}和{bear.get('key_points', [])}"

# 模板方法（简化版）
    def _get_bull_prompt_template(self) -> str:
        return "作为多头分析师，重点关注成长潜力、竞争优势和正面指标..."
    
    def _get_bear_prompt_template(self) -> str:
        return "作为空头分析师，重点关注风险挑战、竞争劣势和负面指标..."
    
    def _get_neutral_prompt_template(self) -> str:
        return "作为中性分析师，客观平衡地分析风险与收益..."
    
    def _get_decision_manager_prompt_template(self) -> str:
        return "作为投资决策管理器，综合评估各方观点，做出最终决策..."

    async def analyze_market_environment(self, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析当前市场环境和情绪"""
        try:
            logger.info("📊 开始分析市场环境...")

            # 获取市场数据
            if not market_data:
                market_data = await self._collect_market_data()

            # 分析市场趋势
            market_trend = await self._analyze_market_trend(market_data)

            # 分析市场情绪
            market_sentiment = await self._analyze_market_sentiment(market_data)

            # 分析市场位置
            market_position = await self._analyze_market_position(market_data)

            # 识别热门板块
            hot_sectors = await self._identify_hot_sectors(market_data)

            # 综合评估
            environment_assessment = {
                "market_trend": market_trend,
                "market_sentiment": market_sentiment,
                "market_position": market_position,
                "hot_sectors": hot_sectors,
                "overall_assessment": self._generate_overall_assessment(
                    market_trend, market_sentiment, market_position, hot_sectors
                ),
                "analysis_timestamp": datetime.now().isoformat()
            }

            logger.info(f"✅ 市场环境分析完成: {environment_assessment['overall_assessment']['market_phase']}")

            return environment_assessment

        except Exception as e:
            logger.error(f"市场环境分析失败: {e}")
            return {"error": str(e)}

    async def _collect_market_data(self) -> Dict[str, Any]:
        """收集市场数据"""
        try:
            # 调用天枢星第一阶段收集
            # 使用服务注册中心替代直接导入
        from backend.core.service_registry import service_registry

            phase1_result = await tianshu_dual_phase_system.execute_phase_1_collection()

            if phase1_result.get("success"):
                return phase1_result.get("report", {})
            else:
                # 降级到本地数据
                return self._get_fallback_market_data()

        except Exception as e:
            logger.error(f"收集市场数据失败: {e}")
            return self._get_fallback_market_data()

    def _get_fallback_market_data(self) -> Dict[str, Any]:
        """不再提供备用市场数据，抛出错误"""
        raise RuntimeError("无法获取市场数据，请检查数据源连接")

    async def _analyze_market_trend(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场趋势"""
        try:
            # 分析指数表现
            indices = market_data.get("market_indices", {})
            avg_change = sum(idx.get("change", 0) for idx in indices.values()) / len(indices) if indices else 0

            # 判断趋势
            if avg_change > 1.5:
                trend = "强势上涨"
                trend_strength = "强"
            elif avg_change > 0.5:
                trend = "温和上涨"
                trend_strength = "中"
            elif avg_change > -0.5:
                trend = "震荡整理"
                trend_strength = "弱"
            elif avg_change > -1.5:
                trend = "温和下跌"
                trend_strength = "中"
            else:
                trend = "强势下跌"
                trend_strength = "强"

            return {
                "trend_direction": trend,
                "trend_strength": trend_strength,
                "average_change": avg_change,
                "indices_performance": indices
            }

        except Exception as e:
            logger.error(f"市场趋势分析失败: {e}")
            return {"trend_direction": "震荡整理", "trend_strength": "弱"}

    async def _analyze_market_sentiment(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场情绪"""
        try:
            # 分析成交量
            volume = market_data.get("market_volume", 0)
            volume_level = "高" if volume > 1000000000000 else "中" if volume > 500000000000 else "低"

            # 分析资金流向
            foreign_inflow = market_data.get("foreign_inflow", 0)
            capital_sentiment = "积极" if foreign_inflow > 0 else "谨慎" if foreign_inflow > -1000000000 else "悲观"

            # 综合情绪评估
            if volume_level == "高" and capital_sentiment == "积极":
                overall_sentiment = "乐观"
            elif volume_level == "低" and capital_sentiment == "悲观":
                overall_sentiment = "悲观"
            else:
                overall_sentiment = "中性"

            return {
                "overall_sentiment": overall_sentiment,
                "volume_level": volume_level,
                "capital_sentiment": capital_sentiment,
                "foreign_inflow": foreign_inflow,
                "volume": volume
            }

        except Exception as e:
            logger.error(f"市场情绪分析失败: {e}")
            return {"overall_sentiment": "中性"}

    async def _analyze_market_position(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场位置"""
        try:
            # 分析指数位置（简化版）
            indices = market_data.get("market_indices", {})
            shanghai_index = indices.get("上证指数", {}).get("value", 3200)

            # 判断市场位置
            if shanghai_index > 3500:
                position = "高位"
                market_phase = "牛市"
            elif shanghai_index > 3000:
                position = "中位"
                market_phase = "震荡市"
            else:
                position = "低位"
                market_phase = "熊市"

            return {
                "market_position": position,
                "market_phase": market_phase,
                "key_index_level": shanghai_index,
                "position_assessment": f"当前上证指数{shanghai_index}点，处于{position}，属于{market_phase}特征"
            }

        except Exception as e:
            logger.error(f"市场位置分析失败: {e}")
            return {"market_position": "中位", "market_phase": "震荡市"}

    async def _identify_hot_sectors(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """识别热门板块"""
        try:
            sector_performance = market_data.get("sector_performance", {})

            # 排序板块表现
            sorted_sectors = sorted(sector_performance.items(), key=lambda x: x[1], reverse=True)

            hot_sectors = [sector for sector, performance in sorted_sectors[:3] if performance > 1.0]
            cold_sectors = [sector for sector, performance in sorted_sectors[-3:] if performance < -0.5]

            return {
                "hot_sectors": hot_sectors,
                "cold_sectors": cold_sectors,
                "sector_ranking": sorted_sectors,
                "sector_analysis": f"热门板块: {', '.join(hot_sectors)}; 冷门板块: {', '.join(cold_sectors)}"
            }

        except Exception as e:
            logger.error(f"热门板块识别失败: {e}")
            return {"hot_sectors": ["科技", "医药"], "cold_sectors": ["地产"]}

    def _generate_overall_assessment(self, trend: Dict, sentiment: Dict,
                                   position: Dict, sectors: Dict) -> Dict[str, Any]:
        """生成综合评估"""
        try:
            # 综合评分
            trend_score = 1 if trend.get("average_change", 0) > 0 else 0
            sentiment_score = 1 if sentiment.get("overall_sentiment") == "乐观" else 0.5 if sentiment.get("overall_sentiment") == "中性" else 0
            position_score = 1 if position.get("market_phase") == "牛市" else 0.5 if position.get("market_phase") == "震荡市" else 0

            overall_score = (trend_score + sentiment_score + position_score) / 3

            # 综合判断
            if overall_score > 0.7:
                market_status = "强势"
                investment_suggestion = "积极配置"
            elif overall_score > 0.4:
                market_status = "震荡"
                investment_suggestion = "谨慎配置"
            else:
                market_status = "弱势"
                investment_suggestion = "防御为主"

            return {
                "overall_score": overall_score,
                "market_status": market_status,
                "market_phase": position.get("market_phase", "震荡市"),
                "investment_suggestion": investment_suggestion,
                "key_factors": [
                    f"趋势: {trend.get('trend_direction', '震荡')}",
                    f"情绪: {sentiment.get('overall_sentiment', '中性')}",
                    f"位置: {position.get('market_position', '中位')}",
                    f"热点: {', '.join(sectors.get('hot_sectors', []))}"
                ]
            }

        except Exception as e:
            logger.error(f"综合评估失败: {e}")
            return {"market_status": "震荡", "market_phase": "震荡市"}

# 全局实例
trading_agents_integration = TradingAgentsIntegration()
