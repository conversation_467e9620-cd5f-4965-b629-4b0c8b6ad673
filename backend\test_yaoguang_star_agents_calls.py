#!/usr/bin/env python3
"""
测试瑶光星调用其他智能体的功能
验证瑶光星是否能正确调用6个星系的智能体
"""

import sys
import asyncio
sys.path.append('.')

async def test_yaoguang_agents_calls():
    """测试瑶光星调用其他智能体"""
    try:
        print('🌟' + '='*80)
        print('🌟 测试瑶光星调用其他智能体功能')
        print('🌟' + '='*80)
        
        from roles.yaoguang_star.core.unified_yaoguang_system import UnifiedYaoguangSystem
        
        # 初始化瑶光星系统
        print('\n🔧 初始化瑶光星统一系统...')
        system = UnifiedYaoguangSystem()
        
        init_result = await system.initialize_system()
        print(f'初始化结果: {"✅ 成功" if init_result.get("success") else "❌ 失败"}')
        
        if not init_result.get("success"):
            print(f'初始化失败原因: {init_result.get("error", "未知错误")}')
            return
        
        # 创建测试会话
        session = {
            'session_id': 'test_agents_call_session',
            'start_time': '2024-01-01',
            'end_time': '2024-01-31',
            'session_type': 'agents_test'
        }
        
        print('\n🎯 测试调用各个星系智能体...')
        
        # 1. 测试开阳星选股
        print('\n1️⃣ 测试开阳星选股智能体...')
        try:
            kaiyang_result = await system._real_kaiyang_stock_selection(session)
            if isinstance(kaiyang_result, list) and len(kaiyang_result) > 0:
                print(f'   ✅ 开阳星选股成功: {len(kaiyang_result)} 只股票')
                print(f'   📋 选股结果: {kaiyang_result[:3]}...')  # 显示前3只
                selected_stocks = kaiyang_result
            else:
                print('   ❌ 开阳星选股失败或无结果')
                selected_stocks = ['000001.XSHE', '000002.XSHE']  # 使用默认股票
        except Exception as e:
            print(f'   ❌ 开阳星选股异常: {e}')
            selected_stocks = ['000001.XSHE', '000002.XSHE']
        
        # 2. 测试天枢星市场分析
        print('\n2️⃣ 测试天枢星市场分析智能体...')
        try:
            tianshu_result = await system._real_tianshu_market_analysis(session)
            if tianshu_result.get('analysis_status') != 'failed':
                print('   ✅ 天枢星市场分析成功')
                print(f'   📊 分析结果: {tianshu_result.get("data_source", "unknown")}')
            else:
                print(f'   ❌ 天枢星市场分析失败: {tianshu_result.get("error", "unknown")}')
        except Exception as e:
            print(f'   ❌ 天枢星市场分析异常: {e}')
        
        # 3. 测试三星分析（天枢+天玑+天璇）
        if selected_stocks:
            print('\n3️⃣ 测试三星分析智能体（天枢+天玑+天璇）...')
            try:
                three_stars_result = await system._real_three_stars_analysis(selected_stocks[0], selected_stocks)
                if three_stars_result.get('analysis_status') != 'failed':
                    print('   ✅ 三星分析成功')
                    print(f'   🔍 分析股票: {selected_stocks[0]}')
                    
                    # 检查各星系的分析结果
                    if 'tianshu_analysis' in three_stars_result:
                        print('   📈 天枢星分析: 完成')
                    if 'tianji_analysis' in three_stars_result:
                        print('   ⚠️ 天玑星分析: 完成')
                    if 'tianxuan_analysis' in three_stars_result:
                        print('   📊 天璇星分析: 完成')
                else:
                    print(f'   ❌ 三星分析失败: {three_stars_result.get("error", "unknown")}')
            except Exception as e:
                print(f'   ❌ 三星分析异常: {e}')
        
        # 4. 测试天权星策略匹配
        print('\n4️⃣ 测试天权星策略匹配智能体...')
        try:
            if system.automation_engine.get("tianquan_strategies"):
                tianquan_service = system.automation_engine["tianquan_strategies"]
                strategy_context = {
                    "market_analysis": {"sentiment": "neutral"},
                    "selected_stocks": selected_stocks[:2],
                    "risk_tolerance": "moderate"
                }
                strategy_result = await tianquan_service.match_strategy(strategy_context)
                if strategy_result.get("success"):
                    print('   ✅ 天权星策略匹配成功')
                    print(f'   🎯 策略类型: {strategy_result.get("strategy_type", "unknown")}')
                else:
                    print(f'   ❌ 天权星策略匹配失败: {strategy_result.get("error", "unknown")}')
            else:
                print('   ⚠️ 天权星策略服务不可用，使用备用服务')
        except Exception as e:
            print(f'   ❌ 天权星策略匹配异常: {e}')
        
        # 5. 测试玉衡星交易执行
        print('\n5️⃣ 测试玉衡星交易执行智能体...')
        try:
            # 导入玉衡星服务
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            
            trading_context = {
                "stock_code": selected_stocks[0] if selected_stocks else "000001.XSHE",
                "trading_mode": "test",
                "initial_capital": 100000,
                "max_position_size": 0.1,
                "session_id": session["session_id"]
            }
            
            execution_result = await yuheng_automation_system.execute_trading_automation(trading_context)
            if execution_result.get("success"):
                print('   ✅ 玉衡星交易执行成功')
                print(f'   💰 执行结果: {execution_result.get("execution_summary", "unknown")}')
            else:
                print(f'   ❌ 玉衡星交易执行失败: {execution_result.get("error", "unknown")}')
        except Exception as e:
            print(f'   ❌ 玉衡星交易执行异常: {e}')
        
        # 6. 测试瑶光星自身的学习协调
        print('\n6️⃣ 测试瑶光星学习协调功能...')
        try:
            from roles.yaoguang_star.yaoguang_coordination_service import yaoguang_coordination_service
            
            learning_config = {
                "learning_focus": ["agent_coordination"],
                "learning_mode": "comprehensive",
                "priority": "high",
                "requester": "agents_test"
            }
            
            coordination_result = await yaoguang_coordination_service.coordinate_learning_session(learning_config)
            if coordination_result.get("success"):
                print('   ✅ 瑶光星学习协调成功')
                print(f'   🎓 协调结果: {coordination_result.get("request_id", "unknown")}')
            else:
                print(f'   ❌ 瑶光星学习协调失败: {coordination_result.get("error", "unknown")}')
        except Exception as e:
            print(f'   ❌ 瑶光星学习协调异常: {e}')
        
        print('\n' + '='*80)
        print('🎉 瑶光星智能体调用测试完成！')
        print('='*80)
        
    except Exception as e:
        print(f'测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_yaoguang_agents_calls())
