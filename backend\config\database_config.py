#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库配置
所有七星角色使用此配置文件获取正确的数据库路径
"""

from pathlib import Path
import os
from typing import Dict, Any

# 数据库基础路径 - 相对于backend目录
# 修复嵌套路径问题：直接使用data目录，不要再加backend前缀
project_root = Path(__file__).parent.parent  # 回到backend目录
DATABASE_BASE_PATH = project_root / "data"

# 统一数据库路径配置 - 瑶光星数据收集标准
DATABASE_PATHS = {
    # 主要数据库 - 三个独立数据库
    "stock_master": DATABASE_BASE_PATH / "stock_master.db",           # 主数据库（股票基本信息）
    "stock_realtime": DATABASE_BASE_PATH / "stock_realtime.db",       # 实时数据库（实时价格数据）
    "stock_historical": DATABASE_BASE_PATH / "stock_historical.db",   # 历史数据库（15GB历史数据）

    # 向后兼容性别名
    "stock_database": DATABASE_BASE_PATH / "stock_master.db",         # 别名：指向主数据库

    # 开阳星专用数据库
    "stock_info_database": DATABASE_BASE_PATH / "stock_info_database.db",  # 股票基本信息数据库
    "realtime_stock_data": DATABASE_BASE_PATH / "realtime_stock_data.db",  # 实时股票数据

    # 智能体系统
    "legendary_memory": DATABASE_BASE_PATH / "legendary_memory.db",
    "news_knowledge": DATABASE_BASE_PATH / "news_knowledge_base.db",
    "risk_database": DATABASE_BASE_PATH / "risk_database.db",
    "trading_execution": DATABASE_BASE_PATH / "trading_execution.db",
    
    # 角色专用数据库
    "tianshu_news_analysis": DATABASE_BASE_PATH / "tianshu_news_analysis.db",
    "tianxuan_trigger_research": DATABASE_BASE_PATH / "tianxuan_trigger_research.db",
    "tianji_strategy_risk": DATABASE_BASE_PATH / "tianji_strategy_risk.db",
    "tianquan_strategies": DATABASE_BASE_PATH / "tianquan_strategies.db",
    "yuheng_execution": DATABASE_BASE_PATH / "yuheng_execution_optimization.db",
    "kaiyang_portfolio": DATABASE_BASE_PATH / "kaiyang_portfolio.db",
    "yaoguang_distribution": DATABASE_BASE_PATH / "yaoguang_distribution.db",
    
    # 系统数据库
    "advanced_execution": DATABASE_BASE_PATH / "advanced_execution_system.db",
    "separated_trading": DATABASE_BASE_PATH / "separated_trading_statistics.db",
    "alerts": DATABASE_BASE_PATH / "alerts.db",
    "decision_making": DATABASE_BASE_PATH / "decision_making.db"
}

def get_database_path(db_name: str) -> str:
    """获取数据库路径"""
    if db_name in DATABASE_PATHS:
        return str(DATABASE_PATHS[db_name])
    else:
        raise ValueError(f"未知的数据库名称: {db_name}")

def get_all_database_paths() -> dict:
    """获取所有数据库路径"""
    return {name: str(path) for name, path in DATABASE_PATHS.items()}

def check_database_exists(db_name: str) -> bool:
    """检查数据库文件是否存在"""
    try:
        db_path = get_database_path(db_name)
        return os.path.exists(db_path)
    except:
        return False

def get_all_database_status() -> Dict[str, Dict[str, Any]]:
    """获取所有数据库状态"""
    from typing import Dict, Any
    import os

    status = {}

    for db_name in DATABASE_PATHS.keys():
        try:
            db_path = get_database_path(db_name)
            exists = os.path.exists(db_path)
            size = os.path.getsize(db_path) if exists else 0

            status[db_name] = {
                "path": db_path,
                "exists": exists,
                "size_bytes": size,
                "size_mb": round(size / 1024 / 1024, 2),
                "description": f"{db_name} database"
            }
        except Exception as e:
            status[db_name] = {
                "error": str(e),
                "exists": False
            }

    return status
