<template>
  <div class="role-page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="role-info">
          <div class="role-avatar">
            <img src="/images/roles/瑶光.png" alt="瑶光星" />
          </div>
          <div class="role-details">
            <h1>瑶光星 - 学习协调绩效中心</h1>
            <p>六星协调绩效、学习回测效果分析与系统优化建议</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="status-indicators">
            <div class="indicator" :class="{ online: performanceData.overview?.coordination_efficiency > 0.8 }">
              <span class="dot"></span>
              <span>协调等级: {{ performanceData.overview?.coordination_grade || 'N/A' }}</span>
            </div>
            <div class="indicator">
              <span class="value">{{ (performanceData.overview?.overall_score * 100).toFixed(1) || '0' }}%</span>
              <span class="label">综合评分</span>
            </div>
          </div>
          <el-button type="primary" @click="refreshPerformance">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心绩效指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card primary">
        <div class="metric-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (performanceData.core_metrics?.coordination_efficiency?.value * 100).toFixed(1) || '0' }}%</div>
          <div class="metric-label">六星协调效率</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            {{ performanceData.core_metrics?.coordination_efficiency?.trend || '+0%' }}
          </div>
          <div class="metric-target">目标: {{ (performanceData.core_metrics?.coordination_efficiency?.target * 100).toFixed(0) || '90' }}%</div>
        </div>
      </div>

      <div class="metric-card success">
        <div class="metric-icon">
          <el-icon><Trophy /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (performanceData.core_metrics?.learning_success_rate?.value * 100).toFixed(1) || '0' }}%</div>
          <div class="metric-label">学习成功率</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            {{ performanceData.core_metrics?.learning_success_rate?.trend || '+0%' }}
          </div>
          <div class="metric-target">目标: {{ (performanceData.core_metrics?.learning_success_rate?.target * 100).toFixed(0) || '85' }}%</div>
        </div>
      </div>

      <div class="metric-card warning">
        <div class="metric-icon">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (performanceData.core_metrics?.backtest_accuracy?.value * 100).toFixed(1) || '0' }}%</div>
          <div class="metric-label">回测准确率</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            {{ performanceData.core_metrics?.backtest_accuracy?.trend || '+0%' }}
          </div>
          <div class="metric-target">目标: {{ (performanceData.core_metrics?.backtest_accuracy?.target * 100).toFixed(0) || '80' }}%</div>
        </div>
      </div>

      <div class="metric-card info">
        <div class="metric-icon">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ (performanceData.core_metrics?.system_stability?.value * 100).toFixed(1) || '0' }}%</div>
          <div class="metric-label">系统稳定性</div>
          <div class="metric-change positive">
            <el-icon><ArrowUp /></el-icon>
            {{ performanceData.core_metrics?.system_stability?.trend || '+0%' }}
          </div>
          <div class="metric-target">目标: {{ (performanceData.core_metrics?.system_stability?.target * 100).toFixed(0) || '95' }}%</div>
        </div>
      </div>
    </div>

    <!-- 绩效图表 -->
    <div class="charts-container">
      <!-- 协调绩效趋势分析 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>协调绩效趋势分析</h3>
              <div class="chart-controls">
                <el-button-group>
                  <el-button
                    v-for="period in performancePeriods"
                    :key="period.value"
                    :type="selectedPerformancePeriod === period.value ? 'primary' : 'default'"
                    @click="changePerformancePeriod(period.value)"
                    size="small"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <div ref="performanceChart" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </div>

      <!-- 六星协调分析 -->
      <div class="chart-card">
        <el-card>
          <template #header>
            <div class="chart-header">
              <h3>六星协调分析</h3>
              <div class="chart-controls">
                <el-select v-model="selectedCoordinationMetric" @change="updateCoordinationChart" size="small">
                  <el-option label="学习协调" value="learning_coordination" />
                  <el-option label="回测协调" value="backtest_coordination" />
                  <el-option label="智能体调用" value="agent_calls" />
                </el-select>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <div ref="coordinationChart" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 六星协调健康状态 -->
    <div class="coordination-health-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>六星协调健康状态</h3>
            <div class="header-actions">
              <el-button @click="refreshCoordinationHealth" size="small">
                <el-icon><Refresh /></el-icon>
                刷新健康状态
              </el-button>
            </div>
          </div>
        </template>
        <div class="coordination-grid">
          <div
            v-for="(star, key) in performanceData.coordination_health"
            :key="key"
            class="coordination-health-card"
            :class="{ excellent: star.status === 'excellent', good: star.status === 'good', warning: star.status === 'warning' }"
          >
            <div class="star-header">
              <div class="star-name">{{ star.name }}</div>
              <div class="star-status">
                <el-tag
                  :type="getStarHealthType(star.status)"
                  size="small"
                >
                  {{ getStarHealthText(star.status) }}
                </el-tag>
              </div>
            </div>
            <div class="star-metrics">
              <div class="metric-row">
                <span class="metric-label">协调评分:</span>
                <el-progress
                  :percentage="star.coordination_score * 100"
                  :color="getHealthColor(star.coordination_score)"
                  :show-text="false"
                />
                <span class="metric-value">{{ (star.coordination_score * 100).toFixed(1) }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">响应时间:</span>
                <span class="metric-value">{{ star.response_time }}ms</span>
              </div>
              <div class="star-description">
                {{ star.description }}
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 学习协调统计 -->
    <div class="learning-coordination-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>学习协调统计</h3>
          </div>
        </template>
        <div class="learning-stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.learning_coordination?.sessions_today || 0 }}</div>
              <div class="stat-label">今日学习会话</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.learning_coordination?.successful_coordinations || 0 }}</div>
              <div class="stat-label">成功协调次数</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.learning_coordination?.avg_coordination_time || '0秒' }}</div>
              <div class="stat-label">平均协调时间</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.learning_coordination?.active_agents || 0 }}</div>
              <div class="stat-label">活跃智能体数</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 回测协调统计 -->
    <div class="backtest-coordination-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>回测协调统计</h3>
          </div>
        </template>
        <div class="backtest-stats-grid">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.backtest_coordination?.backtests_today || 0 }}</div>
              <div class="stat-label">今日回测次数</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.backtest_coordination?.successful_backtests || 0 }}</div>
              <div class="stat-label">成功回测数</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ performanceData.backtest_coordination?.avg_backtest_time || '0秒' }}</div>
              <div class="stat-label">平均回测时间</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><PieChart /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ (performanceData.backtest_coordination?.accuracy_rate * 100).toFixed(1) || '0' }}%</div>
              <div class="stat-label">回测准确率</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系统优化建议 -->
    <div class="optimization-suggestions">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>系统优化建议</h3>
            <div class="header-actions">
              <el-button @click="generateOptimizationReport" size="small">
                <el-icon><Document /></el-icon>
                生成优化报告
              </el-button>
            </div>
          </div>
        </template>
        <div class="suggestions-list">
          <div
            v-for="(suggestion, index) in performanceData.optimization_suggestions"
            :key="index"
            class="suggestion-item"
          >
            <div class="suggestion-icon">
              <el-icon><Opportunity /></el-icon>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.area }}</div>
              <div class="suggestion-description">{{ suggestion.suggestion }}</div>
              <div class="suggestion-metrics">
                <span class="current-value">当前: {{ suggestion.current }}</span>
                <span class="target-value">目标: {{ suggestion.target }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 协调历史记录 -->
    <div class="coordination-history">
      <el-card>
        <template #header>
          <div class="section-header">
            <h3>协调历史记录</h3>
            <div class="header-actions">
              <el-button @click="exportCoordinationData" size="small">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="performanceData.coordination_history?.daily_records || []" v-loading="historyLoading" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="learning_sessions" label="学习会话" width="120" />
          <el-table-column prop="backtest_sessions" label="回测会话" width="120" />
          <el-table-column prop="coordination_efficiency" label="协调效率" width="120">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.coordination_efficiency * 100"
                :color="getEfficiencyColor(scope.row.coordination_efficiency)"
                :show-text="false"
              />
              <span class="efficiency-text">{{ (scope.row.coordination_efficiency * 100).toFixed(1) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="综合评价" width="120">
            <template #default="scope">
              <el-tag :type="getOverallRatingType(scope.row)" size="small">
                {{ getOverallRating(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  ArrowUp,
  ArrowDown,
  Connection,
  Trophy,
  DataAnalysis,
  Monitor,
  DataBoard,
  TrendCharts,
  Timer,
  SuccessFilled,
  PieChart,
  Clock,
  Opportunity,
  Document,
  Download
} from '@element-plus/icons-vue'
import { YaoguangStarAPI } from '@/api/roles/yaoguang-star'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const historyLoading = ref(false)
const performanceData = ref<any>({
  overview: {},
  core_metrics: {},
  coordination_health: {},
  learning_coordination: {},
  backtest_coordination: {},
  coordination_history: { daily_records: [] },
  optimization_suggestions: []
})
const selectedPerformancePeriod = ref('7d')
const selectedCoordinationMetric = ref('learning_coordination')

// 图表相关
const performanceChart = ref()
const coordinationChart = ref()
let performanceChartInstance: echarts.ECharts | null = null
let coordinationChartInstance: echarts.ECharts | null = null

// 绩效周期选项
const performancePeriods = ref([
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' },
  { label: '1年', value: '1y' }
])

// 方法
const loadPerformanceData = async () => {
  try {
    loading.value = true
    const response = await YaoguangStarAPI.getPerformanceDashboard()

    if (response.data) {
      performanceData.value = response.data
    }
  } catch (error) {
    console.error('加载绩效数据失败:', error)
    ElMessage.error('加载绩效数据失败')
  } finally {
    loading.value = false
  }
}

const refreshPerformance = async () => {
  await loadPerformanceData()
  await nextTick()
  updateCharts()
  ElMessage.success('绩效数据刷新成功')
}

const refreshCoordinationHealth = async () => {
  await loadPerformanceData()
}

const changePerformancePeriod = (period: string) => {
  selectedPerformancePeriod.value = period
  updatePerformanceChart()
}

const updateCoordinationChart = () => {
  if (coordinationChartInstance) {
    updateCoordinationPerformanceChart()
  }
}

const generateOptimizationReport = () => {
  ElMessage.success('优化报告生成成功')
}

const exportCoordinationData = () => {
  ElMessage.success('协调数据导出成功')
}

// 辅助方法
const getStarHealthType = (status: string) => {
  switch (status) {
    case 'excellent': return 'success'
    case 'good': return 'primary'
    case 'warning': return 'warning'
    default: return 'info'
  }
}

const getStarHealthText = (status: string) => {
  switch (status) {
    case 'excellent': return '优秀'
    case 'good': return '良好'
    case 'warning': return '警告'
    default: return '未知'
  }
}

const getHealthColor = (score: number) => {
  if (score >= 0.9) return '#67c23a'
  if (score >= 0.8) return '#409eff'
  if (score >= 0.7) return '#e6a23c'
  return '#f56c6c'
}

const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 0.9) return '#67c23a'
  if (efficiency >= 0.8) return '#409eff'
  if (efficiency >= 0.7) return '#e6a23c'
  return '#f56c6c'
}

const getOverallRating = (row: any) => {
  const avg = row.coordination_efficiency
  if (avg >= 0.9) return '优秀'
  if (avg >= 0.8) return '良好'
  if (avg >= 0.7) return '一般'
  return '需改进'
}

const getOverallRatingType = (row: any) => {
  const avg = row.coordination_efficiency
  if (avg >= 0.9) return 'success'
  if (avg >= 0.8) return 'primary'
  if (avg >= 0.7) return 'warning'
  return 'danger'
}

// 图表初始化和更新
const initCharts = async () => {
  await nextTick()

  // 等待DOM完全渲染并且容器有尺寸
  const waitForContainer = () => {
    return new Promise((resolve) => {
      const checkContainer = () => {
        const perfContainer = performanceChart.value
        const coordContainer = coordinationChart.value

        if (perfContainer && coordContainer &&
            perfContainer.clientWidth > 0 && perfContainer.clientHeight > 0 &&
            coordContainer.clientWidth > 0 && coordContainer.clientHeight > 0) {
          resolve(true)
        } else {
          setTimeout(checkContainer, 100)
        }
      }
      checkContainer()
    })
  }

  try {
    await waitForContainer()
    initPerformanceChart()
    initCoordinationChart()
  } catch (error) {
    console.error('图表容器等待超时:', error)
  }
}

let performanceChartRetryCount = 0
const maxRetryCount = 10

const initPerformanceChart = () => {
  if (performanceChart.value && !performanceChartInstance) {
    // 检查DOM元素尺寸
    if (performanceChart.value.clientWidth === 0 || performanceChart.value.clientHeight === 0) {
      performanceChartRetryCount++
      if (performanceChartRetryCount < maxRetryCount) {
        console.warn(`绩效图表容器尺寸为0，延迟初始化 (${performanceChartRetryCount}/${maxRetryCount})`)
        setTimeout(initPerformanceChart, 300)
        return
      } else {
        console.error('绩效图表初始化失败：容器尺寸始终为0')
        return
      }
    }

    try {
      performanceChartInstance = echarts.init(performanceChart.value)
      updatePerformanceChart()
      performanceChartRetryCount = 0 // 重置重试计数
      console.log('✅ 绩效图表初始化成功')
    } catch (error) {
      console.error('绩效图表初始化失败:', error)
    }
  }
}

let coordinationChartRetryCount = 0

const initCoordinationChart = () => {
  if (coordinationChart.value && !coordinationChartInstance) {
    // 检查DOM元素尺寸
    if (coordinationChart.value.clientWidth === 0 || coordinationChart.value.clientHeight === 0) {
      coordinationChartRetryCount++
      if (coordinationChartRetryCount < maxRetryCount) {
        console.warn(`协调图表容器尺寸为0，延迟初始化 (${coordinationChartRetryCount}/${maxRetryCount})`)
        setTimeout(initCoordinationChart, 300)
        return
      } else {
        console.error('协调图表初始化失败：容器尺寸始终为0')
        return
      }
    }

    try {
      coordinationChartInstance = echarts.init(coordinationChart.value)
      updateCoordinationPerformanceChart()
      coordinationChartRetryCount = 0 // 重置重试计数
      console.log('✅ 协调图表初始化成功')
    } catch (error) {
      console.error('协调图表初始化失败:', error)
    }
  }
}

const updateCharts = () => {
  updatePerformanceChart()
  updateCoordinationPerformanceChart()
}

const updatePerformanceChart = () => {
  if (!performanceChartInstance) return

  const dailyData = performanceData.value.coordination_history?.daily_records || []
  const dates = dailyData.map(item => item.date)
  const efficiencyData = dailyData.map(item => (item.coordination_efficiency * 100).toFixed(1))
  const learningData = dailyData.map(item => (item.learning_success_rate * 100).toFixed(1))
  const backtestData = dailyData.map(item => (item.backtest_accuracy * 100).toFixed(1))

  const option = {
    title: {
      text: '协调绩效趋势分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}%<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['协调效率', '学习成功率', '回测准确率'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '评分(%)',
      min: 70,
      max: 100
    },
    series: [
      {
        name: '协调效率',
        type: 'line',
        data: efficiencyData,
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '学习成功率',
        type: 'line',
        data: learningData,
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '回测准确率',
        type: 'line',
        data: backtestData,
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }

  performanceChartInstance.setOption(option)
}

const updateCoordinationPerformanceChart = () => {
  if (!coordinationChartInstance) return

  const coordinationData = performanceData.value.six_stars_coordination || {}

  // 根据选择的指标类型准备数据
  let chartData = []
  let chartTitle = ''

  if (selectedCoordinationMetric.value === 'learning_coordination') {
    chartData = [
      { name: '开阳星', value: coordinationData.kaiyang_learning || 0 },
      { name: '天枢星', value: coordinationData.tianshu_learning || 0 },
      { name: '天玑星', value: coordinationData.tianji_learning || 0 },
      { name: '天璇星', value: coordinationData.tianxuan_learning || 0 },
      { name: '天权星', value: coordinationData.tianquan_learning || 0 },
      { name: '玉衡星', value: coordinationData.yuheng_learning || 0 }
    ]
    chartTitle = '学习协调效率'
  } else if (selectedCoordinationMetric.value === 'backtest_coordination') {
    chartData = [
      { name: '开阳星', value: coordinationData.kaiyang_backtest || 0 },
      { name: '天枢星', value: coordinationData.tianshu_backtest || 0 },
      { name: '天玑星', value: coordinationData.tianji_backtest || 0 },
      { name: '天璇星', value: coordinationData.tianxuan_backtest || 0 },
      { name: '天权星', value: coordinationData.tianquan_backtest || 0 },
      { name: '玉衡星', value: coordinationData.yuheng_backtest || 0 }
    ]
    chartTitle = '回测协调效率'
  } else {
    chartData = [
      { name: '开阳星', value: coordinationData.kaiyang_calls || 0 },
      { name: '天枢星', value: coordinationData.tianshu_calls || 0 },
      { name: '天玑星', value: coordinationData.tianji_calls || 0 },
      { name: '天璇星', value: coordinationData.tianxuan_calls || 0 },
      { name: '天权星', value: coordinationData.tianquan_calls || 0 },
      { name: '玉衡星', value: coordinationData.yuheng_calls || 0 }
    ]
    chartTitle = '智能体调用次数'
  }

  const option = {
    title: {
      text: chartTitle,
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: [
        { name: '开阳星', max: 100 },
        { name: '天枢星', max: 100 },
        { name: '天玑星', max: 100 },
        { name: '天璇星', max: 100 },
        { name: '天权星', max: 100 },
        { name: '玉衡星', max: 100 }
      ]
    },
    series: [
      {
        name: chartTitle,
        type: 'radar',
        data: [
          {
            value: chartData.map(item => item.value),
            name: chartTitle,
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.3)'
            },
            lineStyle: {
              color: '#409eff'
            }
          }
        ]
      }
    ]
  }

  coordinationChartInstance.setOption(option)
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  if (performanceChartInstance) {
    performanceChartInstance.resize()
  }
  if (coordinationChartInstance) {
    coordinationChartInstance.resize()
  }
}

onMounted(() => {
  loadPerformanceData()
  nextTick(() => {
    initCharts()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (performanceChartInstance) {
    performanceChartInstance.dispose()
    performanceChartInstance = null
  }
  if (coordinationChartInstance) {
    coordinationChartInstance.dispose()
    coordinationChartInstance = null
  }
})
</script>

<style scoped>
@import '@/styles/role-pages-common.scss';

/* 瑶光星绩效中心特定样式 */
.role-page-container {
  background: linear-gradient(135deg, #f5f0ff 0%, #e6e0ff 100%);
}

.page-header {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.role-details h1 {
  color: white;
}

.role-details p {
  color: rgba(255, 255, 255, 0.9);
}

.status-indicators {
  display: flex;
  gap: 16px;
  align-items: center;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 14px;
}

.indicator.online .dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator .value {
  font-weight: 600;
  font-size: 16px;
}

.indicator .label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 协调健康状态网格 */
.coordination-health-section {
  margin-bottom: 24px;
}

.coordination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.coordination-health-card {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
  transition: all 0.3s ease;
}

.coordination-health-card.excellent {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.coordination-health-card.good {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.coordination-health-card.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.coordination-health-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.star-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.star-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.star-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  min-width: 60px;
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  min-width: 50px;
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
}

.star-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 学习协调统计 */
.learning-coordination-section,
.backtest-coordination-section {
  margin-bottom: 24px;
}

.learning-stats-grid,
.backtest-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 24px;
  color: #8b5cf6;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

/* 系统优化建议 */
.optimization-suggestions {
  margin-bottom: 24px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.suggestion-icon {
  font-size: 20px;
  margin-top: 2px;
  color: #8b5cf6;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.suggestion-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.suggestion-metrics {
  display: flex;
  gap: 16px;
  align-items: center;
}

.current-value {
  font-size: 12px;
  color: #6b7280;
}

.target-value {
  font-size: 12px;
  font-weight: 600;
  color: #8b5cf6;
}

/* 协调历史记录 */
.coordination-history {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.efficiency-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .status-indicators {
    flex-direction: column;
    gap: 8px;
  }

  .chart-header,
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .chart-controls,
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .coordination-grid {
    grid-template-columns: 1fr;
  }

  .suggestion-item {
    flex-direction: column;
    gap: 12px;
  }
}
</style>