"""
统一实时数据服务 - 集成市场数据、用户数据、策略数据
真实的数据流处理，连接现有数据库和API
合并了realtime_data_service.py的功能，实现大而简的架构
"""

import asyncio
import aiohttp
import sqlite3
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading
import queue
import time

@dataclass
class RealTimeMarketData:
    """实时市场数据"""
    symbol: str
    name: str
    price: float
    change: float
    change_percent: float
    volume: int
    amount: float
    high: float
    low: float
    open: float
    timestamp: datetime
    
    # 技术指标
    ma5: Optional[float] = None
    ma10: Optional[float] = None
    ma20: Optional[float] = None
    rsi: Optional[float] = None
    macd: Optional[float] = None

@dataclass
class UserPortfolioData:
    """用户组合数据"""
    user_id: str
    total_assets: float
    available_cash: float
    market_value: float
    total_profit: float
    profit_rate: float
    positions: List[Dict[str, Any]]
    risk_metrics: Dict[str, float]

@dataclass
class StrategyData:
    """策略数据"""
    strategy_id: str
    strategy_name: str
    status: str
    performance: Dict[str, float]
    positions: List[Dict[str, Any]]
    signals: List[Dict[str, Any]]

class RealTimeDataPipeline:
    """实时数据管道"""
    
    def __init__(self):
        # 数据库连接
        self.stock_db_path = "backend/data/stock_master.db"
        self.realtime_db_path = "backend/data/stock_realtime.db"
        
        # 数据缓存
        self.market_data_cache = {}
        self.user_data_cache = {}
        self.strategy_data_cache = {}
        
        # 数据更新队列
        self.data_queue = queue.Queue()
        self.update_thread = None
        self.is_running = False
        
        # 东方财富API配置
        self.eastmoney_api = {
            "base_url": "http://push2.eastmoney.com/api/qt/clist/get",
            "headers": {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        }
        
        # 初始化数据库
        self._initialize_databases()
    
    def _initialize_databases(self):
        """初始化数据库连接和表结构"""
        try:
            # 检查股票数据库
            conn = sqlite3.connect(self.stock_db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='stock_basic_info'
            """)
            
            if not cursor.fetchone():
                # 创建基础表
                cursor.execute("""
                    CREATE TABLE stock_basic_info (
                        symbol TEXT PRIMARY KEY,
                        name TEXT,
                        industry TEXT,
                        market_cap REAL,
                        pe_ratio REAL,
                        pb_ratio REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            
            conn.commit()
            conn.close()
            
            # 检查实时数据库
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS realtime_quotes (
                    symbol TEXT,
                    price REAL,
                    change_amount REAL,
                    change_percent REAL,
                    volume INTEGER,
                    amount REAL,
                    high REAL,
                    low REAL,
                    open REAL,
                    timestamp TIMESTAMP,
                    PRIMARY KEY (symbol, timestamp)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_portfolios (
                    user_id TEXT,
                    symbol TEXT,
                    quantity INTEGER,
                    avg_cost REAL,
                    current_price REAL,
                    market_value REAL,
                    profit_loss REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (user_id, symbol)
                )
            """)
            
            conn.commit()
            conn.close()
            
            print("✅ 数据库初始化完成")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    async def get_realtime_market_data(self, symbols: List[str]) -> Dict[str, RealTimeMarketData]:
        """获取实时市场数据"""
        try:
            market_data = {}
            
            # 首先从缓存获取
            cached_data = self._get_cached_market_data(symbols)
            market_data.update(cached_data)
            
            # 获取需要更新的股票
            need_update = [s for s in symbols if s not in cached_data or 
                          self._is_data_stale(cached_data.get(s))]
            
            if need_update:
                # 从东方财富API获取实时数据
                fresh_data = await self._fetch_eastmoney_data(need_update)
                market_data.update(fresh_data)
                
                # 更新缓存和数据库
                await self._update_market_data_cache(fresh_data)
                await self._save_market_data_to_db(fresh_data)
            
            return market_data
            
        except Exception as e:
            print(f"❌ 获取实时市场数据失败: {e}")
            return {}
    
    async def get_market_sentiment(self) -> Dict[str, Any]:
        """获取市场情绪 - 使用天枢星新闻情报"""
        try:
            # 使用天枢星的新闻情报收集器获取真实市场情绪
            from roles.tianshu_star.services.intelligence_collectors.news_intelligence_collector import news_intelligence_collector

            news_result = await news_intelligence_collector.collect_news_data(limit=20)
            if news_result.get("success"):
                news_data = news_result.get("data", [])

                # 基于真实新闻数据计算市场情绪
                sentiment_scores = []
                for news in news_data:
                    if "sentiment_score" in news:
                        sentiment_scores.append(news["sentiment_score"])

                if sentiment_scores:
                    overall_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                    return {
                        "overall_sentiment": overall_sentiment,
                        "news_count": len(news_data),
                        "data_source": "tianshu_news_intelligence",
                        "timestamp": datetime.now().isoformat()
                    }

            # 如果无法获取新闻数据，返回空结果
            return {"error": "无法获取市场情绪数据"}

        except Exception as e:
            print(f"获取市场情绪失败: {e}")
            return {"error": str(e)}

    async def get_sector_performance(self) -> Dict[str, Any]:
        """获取板块表现 - 使用动态真实板块数据"""
        try:
            # 使用天枢星的市场分析服务获取动态板块数据
            from roles.tianshu_star.services.market_analysis_service import market_analysis_service

            # 获取当前热门板块和龙头股票
            sector_analysis_result = await market_analysis_service.get_sector_leaders()

            if sector_analysis_result.get("success"):
                sector_leaders = sector_analysis_result.get("sector_leaders", {})

                # 使用统一数据访问服务获取这些龙头股票的实时数据
                from services.unified_data_access_service import unified_data_access_service

                sector_performance = {}
                for sector_name, leader_stocks in sector_leaders.items():
                    try:
                        if not leader_stocks:
                            continue

                        # 获取板块龙头股票的实时数据
                        stock_codes = [stock.get("code") for stock in leader_stocks if stock.get("code")]
                        if not stock_codes:
                            continue

                        result = await unified_data_access_service.get_realtime_stock_data(stock_codes)
                        if result.get("success"):
                            stock_data = result.get("data", [])
                            if stock_data:
                                # 计算板块平均表现
                                changes = [stock.get("change_percent", 0) for stock in stock_data if "change_percent" in stock]
                                volumes = [stock.get("volume_ratio", 1.0) for stock in stock_data if "volume_ratio" in stock]

                                if changes:
                                    avg_change = sum(changes) / len(changes)
                                    avg_volume_ratio = sum(volumes) / len(volumes) if volumes else 1.0

                                    sector_performance[sector_name] = {
                                        "change_percent": round(avg_change, 2),
                                        "volume_ratio": round(avg_volume_ratio, 2),
                                        "stock_count": len(stock_data),
                                        "leader_stocks": [{"name": stock.get("name"), "change": stock.get("change_percent", 0)} for stock in stock_data[:3]],
                                        "data_source": "tianshu_dynamic_analysis"
                                    }
                    except Exception as e:
                        print(f"获取{sector_name}板块数据失败: {e}")
                        continue

                return sector_performance if sector_performance else {"error": "无法获取动态板块数据"}
            else:
                # 如果天枢星服务不可用，使用瑶光星的股票分类数据
                return await self._get_fallback_sector_performance()

        except Exception as e:
            print(f"获取板块表现失败: {e}")
            return await self._get_fallback_sector_performance()

    async def _get_fallback_sector_performance(self) -> Dict[str, Any]:
        """备用板块表现获取方法"""
        try:
            # 使用瑶光星的数据管理服务获取按行业分类的股票
            from roles.yaoguang_star.services.data_management_service import data_management_service

            sectors_result = await data_management_service.get_stocks_by_industry()
            if sectors_result.get("success"):
                industry_stocks = sectors_result.get("industry_stocks", {})

                # 使用统一数据访问服务获取实时数据
                from services.unified_data_access_service import unified_data_access_service

                sector_performance = {}
                for industry, stocks in industry_stocks.items():
                    try:
                        if len(stocks) < 2:  # 至少需要2只股票才能代表板块
                            continue

                        # 取前5只股票作为板块代表
                        representative_stocks = stocks[:5]
                        stock_codes = [stock.get("stock_code") for stock in representative_stocks if stock.get("stock_code")]

                        if stock_codes:
                            result = await unified_data_access_service.get_realtime_stock_data(stock_codes)
                            if result.get("success"):
                                stock_data = result.get("data", [])
                                if stock_data:
                                    # 计算板块平均表现
                                    changes = [stock.get("change_percent", 0) for stock in stock_data if "change_percent" in stock]
                                    if changes:
                                        avg_change = sum(changes) / len(changes)
                                        sector_performance[industry] = {
                                            "change_percent": round(avg_change, 2),
                                            "stock_count": len(stock_data),
                                            "data_source": "yaoguang_industry_classification"
                                        }
                    except Exception as e:
                        print(f"获取{industry}行业数据失败: {e}")
                        continue

                return sector_performance if sector_performance else {"error": "无法获取行业板块数据"}
            else:
                return {"error": "无法获取行业分类数据"}

        except Exception as e:
            print(f"备用板块表现获取失败: {e}")
            return {"error": str(e)}

    async def get_news_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最新新闻事件 - 使用天枢星真实新闻"""
        try:
            # 使用天枢星的新闻收集服务获取真实新闻
            from roles.tianshu_star.services.news_collection_service import news_collection_service

            news_result = await news_collection_service.collect_latest_news(limit=limit)
            if news_result.get("success"):
                return news_result.get("data", [])
            else:
                # 尝试使用新闻情报收集器
                from roles.tianshu_star.services.intelligence_collectors.news_intelligence_collector import news_intelligence_collector

                news_result = await news_intelligence_collector.collect_news_data(limit=limit)
                if news_result.get("success"):
                    return news_result.get("data", [])

            return []

        except Exception as e:
            print(f"获取新闻事件失败: {e}")
            return []

    async def get_stock_recommendations(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取股票推荐 - 使用开阳星和天枢星的真实分析"""
        try:
            # 使用开阳星的股票分析服务
            from roles.kaiyang_star.services.enhanced_stock_analysis_service import enhanced_stock_analysis_service

            # 获取瑶光星的可用股票列表
            from roles.yaoguang_star.services.data_management_service import data_management_service

            available_stocks_result = await data_management_service.get_available_stocks()
            if not available_stocks_result.get("success"):
                return []

            available_stocks = available_stocks_result.get("stocks", [])[:20]  # 取前20只股票进行分析

            recommendations = []
            risk_level = criteria.get("risk_level", "moderate")

            for stock in available_stocks:
                try:
                    stock_code = stock.get("stock_code")
                    stock_name = stock.get("stock_name", f"股票{stock_code}")

                    if not stock_code:
                        continue

                    # 使用开阳星进行技术分析
                    analysis_result = await enhanced_stock_analysis_service.analyze_stock(stock_code)

                    if analysis_result.get("success"):
                        analysis_data = analysis_result.get("analysis", {})

                        # 根据分析结果生成推荐
                        recommendation = {
                            "symbol": stock_code,
                            "name": stock_name,
                            "confidence": analysis_data.get("confidence", 0.5),
                            "risk_level": self._determine_risk_from_analysis(analysis_data),
                            "expected_return": analysis_data.get("expected_return", 0.08),
                            "max_risk": analysis_data.get("max_risk", 0.15),
                            "suggested_quantity": self._calculate_quantity_from_analysis(analysis_data),
                            "price_range": analysis_data.get("price_range", {"min": 0, "max": 0}),
                            "reasoning": analysis_data.get("summary", f"基于开阳星技术分析的{stock_name}投资建议"),
                            "data_source": "kaiyang_technical_analysis"
                        }

                        # 根据风险偏好筛选
                        if self._matches_risk_preference(recommendation, risk_level):
                            recommendations.append(recommendation)

                except Exception as e:
                    print(f"分析股票 {stock_code} 失败: {e}")
                    continue

            # 按置信度排序，返回前3个
            recommendations.sort(key=lambda x: x["confidence"], reverse=True)
            return recommendations[:3]

        except Exception as e:
            print(f"获取股票推荐失败: {e}")
            return []

    def _determine_risk_from_analysis(self, analysis_data: Dict[str, Any]) -> str:
        """根据分析数据确定风险等级"""
        volatility = analysis_data.get("volatility", 0.15)
        if volatility < 0.1:
            return "low"
        elif volatility > 0.2:
            return "high"
        else:
            return "medium"

    def _calculate_quantity_from_analysis(self, analysis_data: Dict[str, Any]) -> int:
        """根据分析数据计算建议数量"""
        confidence = analysis_data.get("confidence", 0.5)
        if confidence > 0.8:
            return 1000
        elif confidence > 0.6:
            return 500
        else:
            return 200

    def _matches_risk_preference(self, recommendation: Dict[str, Any], risk_level: str) -> bool:
        """检查推荐是否匹配风险偏好"""
        rec_risk = recommendation.get("risk_level", "medium")

        if risk_level == "conservative":
            return rec_risk == "low"
        elif risk_level == "aggressive":
            return rec_risk in ["medium", "high"]
        else:  # moderate
            return rec_risk in ["low", "medium"]



    async def get_user_portfolio_data(self, user_id: str) -> Optional[UserPortfolioData]:
        """获取用户组合数据"""
        try:
            # 从缓存获取
            if user_id in self.user_data_cache:
                cache_time = self.user_data_cache[user_id].get('timestamp', 0)
                if time.time() - cache_time < 30:  # 30秒缓存
                    return self.user_data_cache[user_id]['data']
            
            # 从数据库获取用户持仓
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT symbol, quantity, avg_cost, current_price, market_value, profit_loss
                FROM user_portfolios 
                WHERE user_id = ?
            """, (user_id,))
            
            positions = []
            total_market_value = 0
            total_profit = 0
            
            for row in cursor.fetchall():
                symbol, quantity, avg_cost, current_price, market_value, profit_loss = row
                
                position = {
                    "symbol": symbol,
                    "quantity": quantity,
                    "avg_cost": avg_cost,
                    "current_price": current_price,
                    "market_value": market_value,
                    "profit_loss": profit_loss,
                    "profit_rate": (profit_loss / (avg_cost * quantity)) * 100 if avg_cost * quantity > 0 else 0
                }
                positions.append(position)
                total_market_value += market_value
                total_profit += profit_loss
            
            conn.close()
            
            # 计算风险指标
            risk_metrics = self._calculate_portfolio_risk_metrics(positions)
            
            portfolio_data = UserPortfolioData(
                user_id=user_id,
                total_assets=total_market_value + 50000,  # 假设5万现金
                available_cash=50000,
                market_value=total_market_value,
                total_profit=total_profit,
                profit_rate=(total_profit / total_market_value) * 100 if total_market_value > 0 else 0,
                positions=positions,
                risk_metrics=risk_metrics
            )
            
            # 更新缓存
            self.user_data_cache[user_id] = {
                'data': portfolio_data,
                'timestamp': time.time()
            }
            
            return portfolio_data
            
        except Exception as e:
            print(f"❌ 获取用户组合数据失败: {e}")
            return None
    
    async def get_strategy_data(self, strategy_ids: List[str]) -> Dict[str, StrategyData]:
        """获取策略数据"""
        try:
            strategy_data = {}

            # 从数据库获取真实策略数据
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()

            # 创建策略表（如果不存在）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    strategy_id TEXT PRIMARY KEY,
                    strategy_name TEXT,
                    status TEXT,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            for strategy_id in strategy_ids:
                cursor.execute("""
                    SELECT strategy_name, status, total_return, sharpe_ratio,
                           max_drawdown, win_rate
                    FROM strategy_performance
                    WHERE strategy_id = ?
                """, (strategy_id,))

                row = cursor.fetchone()
                if row:
                    strategy = StrategyData(
                        strategy_id=strategy_id,
                        strategy_name=row[0],
                        status=row[1],
                        performance={
                            "total_return": row[2],
                            "sharpe_ratio": row[3],
                            "max_drawdown": row[4],
                            "win_rate": row[5]
                        },
                        positions=[],
                        signals=[]
                    )
                else:
                    # 如果数据库中没有数据，创建默认策略
                    strategy = StrategyData(
                        strategy_id=strategy_id,
                        strategy_name=f"策略_{strategy_id}",
                        status="inactive",
                        performance={
                            "total_return": 0.0,
                            "sharpe_ratio": 0.0,
                            "max_drawdown": 0.0,
                            "win_rate": 0.0
                        },
                        positions=[],
                        signals=[]
                    )

                    # 插入默认数据
                    cursor.execute("""
                        INSERT OR REPLACE INTO strategy_performance
                        (strategy_id, strategy_name, status, total_return,
                         sharpe_ratio, max_drawdown, win_rate)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        strategy_id, strategy.strategy_name, strategy.status,
                        0.0, 0.0, 0.0, 0.0
                    ))

                strategy_data[strategy_id] = strategy

            conn.commit()
            conn.close()
            return strategy_data

        except Exception as e:
            print(f"❌ 获取策略数据失败: {e}")
            return {}
    
    async def _fetch_eastmoney_data(self, symbols: List[str]) -> Dict[str, RealTimeMarketData]:
        """从东方财富API获取数据"""
        try:
            market_data = {}
            
            # 构建secids参数
            secids = []
            for symbol in symbols:
                if symbol.endswith('.SZ'):
                    secids.append(f"0.{symbol[:6]}")
                elif symbol.endswith('.SH'):
                    secids.append(f"1.{symbol[:6]}")
                else:
                    # 默认深圳
                    secids.append(f"0.{symbol}")
            
            # API参数
            params = {
                "cb": "jQuery",
                "pn": "1",
                "pz": str(len(secids)),
                "po": "1",
                "np": "1",
                "ut": "bd1d9ddb04089700cf9c27f6f7426281",
                "fltt": "2",
                "invt": "2",
                "fid": "f3",
                "fs": "m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23",
                "fields": "f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152",
                "secids": ",".join(secids)
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.eastmoney_api["base_url"],
                    params=params,
                    headers=self.eastmoney_api["headers"]
                ) as response:
                    if response.status == 200:
                        text = await response.text()
                        # 解析JSONP响应
                        json_str = text[text.find('(')+1:text.rfind(')')]
                        data = json.loads(json_str)
                        
                        if data.get('data') and data['data'].get('diff'):
                            for item in data['data']['diff']:
                                symbol = f"{item['f12']}.{'SZ' if item['f13'] == 0 else 'SH'}"
                                
                                market_data[symbol] = RealTimeMarketData(
                                    symbol=symbol,
                                    name=item.get('f14', ''),
                                    price=item.get('f2', 0) / 100 if item.get('f2') else 0,
                                    change=item.get('f4', 0) / 100 if item.get('f4') else 0,
                                    change_percent=item.get('f3', 0) / 100 if item.get('f3') else 0,
                                    volume=item.get('f5', 0),
                                    amount=item.get('f6', 0),
                                    high=item.get('f15', 0) / 100 if item.get('f15') else 0,
                                    low=item.get('f16', 0) / 100 if item.get('f16') else 0,
                                    open=item.get('f17', 0) / 100 if item.get('f17') else 0,
                                    timestamp=datetime.now()
                                )
            
            return market_data
            
        except Exception as e:
            print(f"❌ 从东方财富API获取数据失败: {e}")
            # 返回模拟数据作为备用
            return self._generate_mock_market_data(symbols)
    
    def _generate_mock_market_data(self, symbols: List[str]) -> Dict[str, RealTimeMarketData]:
        """不再生成模拟数据，抛出错误"""
        raise RuntimeError(f"无法获取股票 {symbols} 的实时数据，请检查数据源连接")
    
    def _get_cached_market_data(self, symbols: List[str]) -> Dict[str, RealTimeMarketData]:
        """从缓存获取市场数据"""
        cached_data = {}
        
        for symbol in symbols:
            if symbol in self.market_data_cache:
                cache_entry = self.market_data_cache[symbol]
                if time.time() - cache_entry['timestamp'] < 5:  # 5秒缓存
                    cached_data[symbol] = cache_entry['data']
        
        return cached_data
    
    def _is_data_stale(self, data: RealTimeMarketData) -> bool:
        """检查数据是否过期"""
        if not data:
            return True
        
        # 如果数据超过5秒就认为过期
        return (datetime.now() - data.timestamp).total_seconds() > 5
    
    async def _update_market_data_cache(self, market_data: Dict[str, RealTimeMarketData]):
        """更新市场数据缓存"""
        for symbol, data in market_data.items():
            self.market_data_cache[symbol] = {
                'data': data,
                'timestamp': time.time()
            }
    
    async def _save_market_data_to_db(self, market_data: Dict[str, RealTimeMarketData]):
        """保存市场数据到数据库"""
        try:
            conn = sqlite3.connect(self.realtime_db_path)
            cursor = conn.cursor()
            
            for symbol, data in market_data.items():
                cursor.execute("""
                    INSERT OR REPLACE INTO realtime_quotes 
                    (symbol, price, change_amount, change_percent, volume, amount, 
                     high, low, open, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data.symbol, data.price, data.change, data.change_percent,
                    data.volume, data.amount, data.high, data.low, data.open,
                    data.timestamp
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 保存市场数据到数据库失败: {e}")
    
    def _calculate_portfolio_risk_metrics(self, positions: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算组合风险指标"""
        if not positions:
            return {}
        
        # 计算集中度
        total_value = sum(pos['market_value'] for pos in positions)
        max_position = max(pos['market_value'] for pos in positions) if positions else 0
        concentration = (max_position / total_value) if total_value > 0 else 0
        
        # 计算波动率（简化）
        profit_rates = [pos['profit_rate'] for pos in positions]
        volatility = np.std(profit_rates) if len(profit_rates) > 1 else 0
        
        return {
            "concentration": concentration,
            "volatility": volatility,
            "position_count": len(positions),
            "total_value": total_value
        }

# 全局实例
realtime_data_pipeline = RealTimeDataPipeline()

# 便捷函数
async def get_realtime_market_data(symbols: List[str]) -> Dict[str, RealTimeMarketData]:
    """获取实时市场数据"""
    return await realtime_data_pipeline.get_realtime_market_data(symbols)

async def get_user_portfolio_data(user_id: str) -> Optional[UserPortfolioData]:
    """获取用户组合数据"""
    return await realtime_data_pipeline.get_user_portfolio_data(user_id)
